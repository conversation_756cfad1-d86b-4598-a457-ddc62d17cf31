#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules/xss/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules/xss/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules/xss/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules/xss/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xss@1.0.15/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../xss/bin/xss" "$@"
else
  exec node  "$basedir/../xss/bin/xss" "$@"
fi
