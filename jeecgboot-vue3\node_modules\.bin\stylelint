#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules/stylelint/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules/stylelint/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules/stylelint/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules/stylelint/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint@16.22.0_typescript@4.9.5/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
else
  exec node  "$basedir/../stylelint/bin/stylelint.mjs" "$@"
fi
