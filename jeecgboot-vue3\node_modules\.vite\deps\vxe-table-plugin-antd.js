import {
  require_xe_utils
} from "./chunk-KS23GK5M.js";
import {
  require_vue
} from "./chunk-TMYOOG4Z.js";
import "./chunk-5GOUW5RB.js";
import "./chunk-XQVMI6UO.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/vxe-table-plugin-antd@4.0.7_vxe-table@4.6.17/node_modules/vxe-table-plugin-antd/dist/index.common.js
var require_index_common = __commonJS({
  "node_modules/.pnpm/vxe-table-plugin-antd@4.0.7_vxe-table@4.6.17/node_modules/vxe-table-plugin-antd/dist/index.common.js"(exports) {
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports["default"] = exports.VXETablePluginAntd = void 0;
    var _vue = require_vue();
    var _xeUtils = _interopRequireDefault(require_xe_utils());
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { "default": obj };
    }
    function _typeof(o) {
      "@babel/helpers - typeof";
      return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o2) {
        return typeof o2;
      } : function(o2) {
        return o2 && "function" == typeof Symbol && o2.constructor === Symbol && o2 !== Symbol.prototype ? "symbol" : typeof o2;
      }, _typeof(o);
    }
    function ownKeys(e, r) {
      var t = Object.keys(e);
      if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r2) {
          return Object.getOwnPropertyDescriptor(e, r2).enumerable;
        })), t.push.apply(t, o);
      }
      return t;
    }
    function _objectSpread(e) {
      for (var r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), true).forEach(function(r2) {
          _defineProperty(e, r2, t[r2]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r2) {
          Object.defineProperty(e, r2, Object.getOwnPropertyDescriptor(t, r2));
        });
      }
      return e;
    }
    function _defineProperty(obj, key, value) {
      key = _toPropertyKey(key);
      if (key in obj) {
        Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
      } else {
        obj[key] = value;
      }
      return obj;
    }
    function _toPropertyKey(t) {
      var i = _toPrimitive(t, "string");
      return "symbol" == _typeof(i) ? i : i + "";
    }
    function _toPrimitive(t, r) {
      if ("object" != _typeof(t) || !t) return t;
      var e = t[Symbol.toPrimitive];
      if (void 0 !== e) {
        var i = e.call(t, r || "default");
        if ("object" != _typeof(i)) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
      }
      return ("string" === r ? String : Number)(t);
    }
    function isEmptyValue(cellValue) {
      return cellValue === null || cellValue === void 0 || cellValue === "";
    }
    function getOnName(type) {
      return "on" + type.substring(0, 1).toLocaleUpperCase() + type.substring(1);
    }
    function getModelProp(renderOpts) {
      var prop = "value";
      switch (renderOpts.name) {
        case "ASwitch":
          prop = "checked";
          break;
      }
      return prop;
    }
    function getModelEvent(renderOpts) {
      var type = "update:value";
      switch (renderOpts.name) {
        case "ASwitch":
          type = "update:checked";
          break;
      }
      return type;
    }
    function dateFormatToVxeFormat(format) {
      if (format) {
        return "".concat(format).replace("YYYY", "yyyy").replace("DD", "dd");
      }
      return format;
    }
    function getChangeEvent(renderOpts) {
      return "change";
    }
    function getCellEditFilterProps(renderOpts, params, value, defaultProps) {
      return _xeUtils["default"].assign({}, defaultProps, renderOpts.props, _defineProperty({}, getModelProp(renderOpts), value));
    }
    function getItemProps(renderOpts, params, value, defaultProps) {
      return _xeUtils["default"].assign({}, defaultProps, renderOpts.props, _defineProperty({}, getModelProp(renderOpts), value));
    }
    function formatText(cellValue) {
      return "" + (isEmptyValue(cellValue) ? "" : cellValue);
    }
    function getCellLabelVNs(renderOpts, params, cellLabel) {
      var placeholder = renderOpts.placeholder;
      return [(0, _vue.h)("span", {
        "class": "vxe-cell--label"
      }, placeholder && isEmptyValue(cellLabel) ? [(0, _vue.h)("span", {
        "class": "vxe-cell--placeholder"
      }, formatText(placeholder))] : formatText(cellLabel))];
    }
    function getOns(renderOpts, params, inputFunc, changeFunc) {
      var events = renderOpts.events;
      var modelEvent = getModelEvent(renderOpts);
      var changeEvent = getChangeEvent(renderOpts);
      var isSameEvent = changeEvent === modelEvent;
      var ons = {};
      _xeUtils["default"].objectEach(events, function(func, key) {
        ons[getOnName(key)] = function() {
          for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
            args[_key] = arguments[_key];
          }
          func.apply(void 0, [params].concat(args));
        };
      });
      if (inputFunc) {
        ons[getOnName(modelEvent)] = function(targetEvnt) {
          inputFunc(targetEvnt);
          if (events && events[modelEvent]) {
            events[modelEvent](params, targetEvnt);
          }
          if (isSameEvent && changeFunc) {
            changeFunc(targetEvnt);
          }
        };
      }
      if (!isSameEvent && changeFunc) {
        ons[getOnName(changeEvent)] = function() {
          for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
            args[_key2] = arguments[_key2];
          }
          changeFunc.apply(void 0, args);
          if (events && events[changeEvent]) {
            events[changeEvent].apply(events, [params].concat(args));
          }
        };
      }
      return ons;
    }
    function getEditOns(renderOpts, params) {
      var $table = params.$table, row = params.row, column = params.column;
      return getOns(renderOpts, params, function(value) {
        _xeUtils["default"].set(row, column.field, value);
      }, function() {
        $table.updateStatus(params);
      });
    }
    function getFilterOns(renderOpts, params, option, changeFunc) {
      return getOns(renderOpts, params, function(value) {
        option.data = value;
      }, changeFunc);
    }
    function getItemOns(renderOpts, params) {
      var $form = params.$form, data = params.data, field = params.field;
      return getOns(renderOpts, params, function(value) {
        _xeUtils["default"].set(data, field, value);
      }, function() {
        $form.updateStatus(params);
      });
    }
    function matchCascaderData(index, list, values, labels) {
      var val = values[index];
      if (list && values.length > index) {
        _xeUtils["default"].each(list, function(item) {
          if (item.value === val) {
            labels.push(item.label);
            matchCascaderData(++index, item.children, values, labels);
          }
        });
      }
    }
    function formatDatePicker(defaultFormat) {
      return function(renderOpts, params) {
        return getCellLabelVNs(renderOpts, params, getDatePickerCellValue(renderOpts, params, defaultFormat));
      };
    }
    function getSelectCellValue(renderOpts, params) {
      var _renderOpts$options = renderOpts.options, options = _renderOpts$options === void 0 ? [] : _renderOpts$options, optionGroups = renderOpts.optionGroups, _renderOpts$props = renderOpts.props, props = _renderOpts$props === void 0 ? {} : _renderOpts$props, _renderOpts$optionPro = renderOpts.optionProps, optionProps = _renderOpts$optionPro === void 0 ? {} : _renderOpts$optionPro, _renderOpts$optionGro = renderOpts.optionGroupProps, optionGroupProps = _renderOpts$optionGro === void 0 ? {} : _renderOpts$optionGro;
      var row = params.row, column = params.column;
      var labelProp = optionProps.label || "label";
      var valueProp = optionProps.value || "value";
      var groupOptions = optionGroupProps.options || "options";
      var cellValue = _xeUtils["default"].get(row, column.field);
      if (!isEmptyValue(cellValue)) {
        return _xeUtils["default"].map(props.mode === "multiple" ? cellValue : [cellValue], optionGroups ? function(value) {
          var selectItem;
          for (var index = 0; index < optionGroups.length; index++) {
            selectItem = _xeUtils["default"].find(optionGroups[index][groupOptions], function(item) {
              return item[valueProp] === value;
            });
            if (selectItem) {
              break;
            }
          }
          return selectItem ? selectItem[labelProp] : value;
        } : function(value) {
          var selectItem = _xeUtils["default"].find(options, function(item) {
            return item[valueProp] === value;
          });
          return selectItem ? selectItem[labelProp] : value;
        }).join(", ");
      }
      return "";
    }
    function getCascaderCellValue(renderOpts, params) {
      var _renderOpts$props2 = renderOpts.props, props = _renderOpts$props2 === void 0 ? {} : _renderOpts$props2;
      var row = params.row, column = params.column;
      var cellValue = _xeUtils["default"].get(row, column.field);
      var values = cellValue || [];
      var labels = [];
      matchCascaderData(0, props.options, values, labels);
      return (props.showAllLevels === false ? labels.slice(labels.length - 1, labels.length) : labels).join(" ".concat(props.separator || "/", " "));
    }
    function getRangePickerCellValue(renderOpts, params) {
      var _renderOpts$props3 = renderOpts.props, props = _renderOpts$props3 === void 0 ? {} : _renderOpts$props3;
      var row = params.row, column = params.column;
      var cellValue = _xeUtils["default"].get(row, column.field);
      if (cellValue) {
        cellValue = _xeUtils["default"].map(cellValue, function(date) {
          return date && date.format ? date.format(props.format || "YYYY-MM-DD") : _xeUtils["default"].toDateString(date, dateFormatToVxeFormat(props.format || "YYYY-MM-DD"));
        }).join(" ~ ");
      }
      return cellValue;
    }
    function getTreeSelectCellValue(renderOpts, params) {
      var _renderOpts$props4 = renderOpts.props, props = _renderOpts$props4 === void 0 ? {} : _renderOpts$props4;
      var treeData = props.treeData, treeCheckable = props.treeCheckable;
      var row = params.row, column = params.column;
      var cellValue = _xeUtils["default"].get(row, column.field);
      if (!isEmptyValue(cellValue)) {
        return _xeUtils["default"].map(treeCheckable ? cellValue : [cellValue], function(value) {
          var matchObj = _xeUtils["default"].findTree(treeData, function(item) {
            return item.value === value;
          }, {
            children: "children"
          });
          return matchObj ? matchObj.item.title : value;
        }).join(", ");
      }
      return cellValue;
    }
    function getDatePickerCellValue(renderOpts, params, defaultFormat) {
      var _renderOpts$props5 = renderOpts.props, props = _renderOpts$props5 === void 0 ? {} : _renderOpts$props5;
      var row = params.row, column = params.column;
      var cellValue = _xeUtils["default"].get(row, column.field);
      if (cellValue) {
        cellValue = cellValue.format ? cellValue.format(props.format || defaultFormat) : _xeUtils["default"].toDateString(cellValue, dateFormatToVxeFormat(props.format || defaultFormat));
      }
      return cellValue;
    }
    function createEditRender(defaultProps) {
      return function(renderOpts, params) {
        var row = params.row, column = params.column;
        var name = renderOpts.name, attrs = renderOpts.attrs;
        var cellValue = _xeUtils["default"].get(row, column.field);
        return [(0, _vue.h)((0, _vue.resolveComponent)(name), _objectSpread(_objectSpread(_objectSpread({}, attrs), getCellEditFilterProps(renderOpts, params, cellValue, defaultProps)), getEditOns(renderOpts, params)))];
      };
    }
    function defaultButtonEditRender(renderOpts, params) {
      var attrs = renderOpts.attrs;
      return [(0, _vue.h)((0, _vue.resolveComponent)("a-button"), _objectSpread(_objectSpread(_objectSpread({}, attrs), getCellEditFilterProps(renderOpts, params, null)), getOns(renderOpts, params)), cellText(renderOpts.content))];
    }
    function defaultButtonsEditRender(renderOpts, params) {
      var children = renderOpts.children;
      if (children) {
        return children.map(function(childRenderOpts) {
          return defaultButtonEditRender(childRenderOpts, params)[0];
        });
      }
      return [];
    }
    function createFilterRender(defaultProps) {
      return function(renderOpts, params) {
        var column = params.column;
        var name = renderOpts.name, attrs = renderOpts.attrs;
        return [(0, _vue.h)("div", {
          "class": "vxe-table--filter-antd-wrapper"
        }, column.filters.map(function(option, oIndex) {
          var optionValue = option.data;
          return (0, _vue.h)((0, _vue.resolveComponent)(name), _objectSpread(_objectSpread(_objectSpread({
            key: oIndex
          }, attrs), getCellEditFilterProps(renderOpts, params, optionValue, defaultProps)), getFilterOns(renderOpts, params, option, function() {
            handleConfirmFilter(params, !!option.data, option);
          })));
        }))];
      };
    }
    function handleConfirmFilter(params, checked, option) {
      var $panel = params.$panel;
      $panel.changeOption(null, checked, option);
    }
    function defaultFuzzyFilterMethod(params) {
      var option = params.option, row = params.row, column = params.column;
      var data = option.data;
      var cellValue = _xeUtils["default"].get(row, column.field);
      return _xeUtils["default"].toValueString(cellValue).indexOf(data) > -1;
    }
    function defaultExactFilterMethod(params) {
      var option = params.option, row = params.row, column = params.column;
      var data = option.data;
      var cellValue = _xeUtils["default"].get(row, column.field);
      return cellValue === data;
    }
    function cellText(cellValue) {
      return [formatText(cellValue)];
    }
    function renderOptions(options, optionProps) {
      var labelProp = optionProps.label || "label";
      var valueProp = optionProps.value || "value";
      return _xeUtils["default"].map(options, function(item, oIndex) {
        return (0, _vue.h)((0, _vue.resolveComponent)("a-select-option"), {
          key: oIndex,
          value: item[valueProp],
          disabled: item.disabled
        }, {
          "default": function _default() {
            return cellText(item[labelProp]);
          }
        });
      });
    }
    function createFormItemRender(defaultProps) {
      return function(renderOpts, params) {
        var data = params.data, field = params.field;
        var name = renderOpts.name;
        var attrs = renderOpts.attrs;
        var itemValue = _xeUtils["default"].get(data, field);
        return [(0, _vue.h)((0, _vue.resolveComponent)(name), _objectSpread(_objectSpread(_objectSpread({}, attrs), getItemProps(renderOpts, params, itemValue, defaultProps)), getItemOns(renderOpts, params)))];
      };
    }
    function defaultButtonItemRender(renderOpts, params) {
      var attrs = renderOpts.attrs;
      var props = getItemProps(renderOpts, params, null);
      return [(0, _vue.h)((0, _vue.resolveComponent)("a-button"), _objectSpread(_objectSpread(_objectSpread({}, attrs), props), getItemOns(renderOpts, params)), {
        "default": function _default() {
          return cellText(renderOpts.content || props.content);
        }
      })];
    }
    function defaultButtonsItemRender(renderOpts, params) {
      var children = renderOpts.children;
      if (children) {
        return children.map(function(childRenderOpts) {
          return defaultButtonItemRender(childRenderOpts, params)[0];
        });
      }
      return [];
    }
    function createDatePickerExportMethod(defaultFormat) {
      return function(params) {
        var row = params.row, column = params.column, options = params.options;
        return options && options.original ? _xeUtils["default"].get(row, column.field) : getDatePickerCellValue(column.editRender || column.cellRender, params, defaultFormat);
      };
    }
    function createExportMethod(getExportCellValue) {
      return function(params) {
        var row = params.row, column = params.column, options = params.options;
        return options && options.original ? _xeUtils["default"].get(row, column.field) : getExportCellValue(column.editRender || column.cellRender, params);
      };
    }
    function createFormItemRadioAndCheckboxRender() {
      return function(renderOpts, params) {
        var name = renderOpts.name, _renderOpts$options2 = renderOpts.options, options = _renderOpts$options2 === void 0 ? [] : _renderOpts$options2, _renderOpts$optionPro2 = renderOpts.optionProps, optionProps = _renderOpts$optionPro2 === void 0 ? {} : _renderOpts$optionPro2;
        var data = params.data, field = params.field;
        var attrs = renderOpts.attrs;
        var labelProp = optionProps.label || "label";
        var valueProp = optionProps.value || "value";
        var itemValue = _xeUtils["default"].get(data, field);
        return [(0, _vue.h)((0, _vue.resolveComponent)("".concat(name, "Group")), _objectSpread(_objectSpread(_objectSpread({}, attrs), getItemProps(renderOpts, params, itemValue)), getItemOns(renderOpts, params)), {
          "default": function _default() {
            return options.map(function(option, oIndex) {
              return (0, _vue.h)((0, _vue.resolveComponent)(name), {
                key: oIndex,
                value: option[valueProp],
                disabled: option.disabled
              }, {
                "default": function _default3() {
                  return cellText(option[labelProp]);
                }
              });
            });
          }
        })];
      };
    }
    function getEventTargetNode(evnt, container, className) {
      var targetElem;
      var target = evnt.target;
      while (target && target.nodeType && target !== document) {
        if (className && target.className && target.className.split && target.className.split(" ").indexOf(className) > -1) {
          targetElem = target;
        } else if (target === container) {
          return {
            flag: className ? !!targetElem : true,
            container,
            targetElem
          };
        }
        target = target.parentNode;
      }
      return {
        flag: false
      };
    }
    function handleClearEvent(params) {
      var $event = params.$event;
      var bodyElem = document.body;
      if (
        // 下拉框
        getEventTargetNode($event, bodyElem, "ant-select-dropdown").flag || // 级联
        getEventTargetNode($event, bodyElem, "ant-cascader-menus").flag || // 日期
        getEventTargetNode($event, bodyElem, "ant-picker-dropdown").flag || getEventTargetNode($event, bodyElem, "ant-calendar-picker-container").flag || // 时间选择
        getEventTargetNode($event, bodyElem, "ant-time-picker-panel").flag
      ) {
        return false;
      }
    }
    var VXETablePluginAntd = exports.VXETablePluginAntd = {
      install: function install(vxetable) {
        if (!/^(4)\./.test(vxetable.version) && !/v4/i.test(vxetable.v)) {
          console.error("[vxe-table-plugin-antd 4.x] Version vxe-table 4.x is required");
        }
        vxetable.renderer.mixin({
          AAutoComplete: {
            autofocus: "input.ant-input",
            renderDefault: createEditRender(),
            renderEdit: createEditRender(),
            renderFilter: createFilterRender(),
            defaultFilterMethod: defaultExactFilterMethod,
            renderItemContent: createFormItemRender()
          },
          AInput: {
            autofocus: "input.ant-input",
            renderDefault: createEditRender(),
            renderEdit: createEditRender(),
            renderFilter: createFilterRender(),
            defaultFilterMethod: defaultFuzzyFilterMethod,
            renderItemContent: createFormItemRender()
          },
          AInputNumber: {
            autofocus: "input.ant-input-number-input",
            renderDefault: createEditRender(),
            renderEdit: createEditRender(),
            renderFilter: createFilterRender(),
            defaultFilterMethod: defaultFuzzyFilterMethod,
            renderItemContent: createFormItemRender()
          },
          ASelect: {
            renderEdit: function renderEdit(renderOpts, params) {
              var _renderOpts$options3 = renderOpts.options, options = _renderOpts$options3 === void 0 ? [] : _renderOpts$options3, optionGroups = renderOpts.optionGroups, _renderOpts$optionPro3 = renderOpts.optionProps, optionProps = _renderOpts$optionPro3 === void 0 ? {} : _renderOpts$optionPro3, _renderOpts$optionGro2 = renderOpts.optionGroupProps, optionGroupProps = _renderOpts$optionGro2 === void 0 ? {} : _renderOpts$optionGro2;
              var row = params.row, column = params.column;
              var attrs = renderOpts.attrs;
              var cellValue = _xeUtils["default"].get(row, column.field);
              var props = getCellEditFilterProps(renderOpts, params, cellValue);
              var ons = getEditOns(renderOpts, params);
              if (optionGroups) {
                var groupOptions = optionGroupProps.options || "options";
                var groupLabel = optionGroupProps.label || "label";
                return [(0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({}, props), attrs), ons), {
                  "default": function _default() {
                    return _xeUtils["default"].map(optionGroups, function(group, gIndex) {
                      return (0, _vue.h)((0, _vue.resolveComponent)("a-select-opt-group"), {
                        key: gIndex
                      }, {
                        label: function label() {
                          return (0, _vue.h)("span", {}, group[groupLabel]);
                        },
                        "default": function _default3() {
                          return renderOptions(group[groupOptions], optionProps);
                        }
                      });
                    });
                  }
                })];
              }
              return [(0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({}, props), attrs), ons), {
                "default": function _default() {
                  return renderOptions(options, optionProps);
                }
              })];
            },
            renderCell: function renderCell(renderOpts, params) {
              return getCellLabelVNs(renderOpts, params, getSelectCellValue(renderOpts, params));
            },
            renderFilter: function renderFilter(renderOpts, params) {
              var _renderOpts$options4 = renderOpts.options, options = _renderOpts$options4 === void 0 ? [] : _renderOpts$options4, optionGroups = renderOpts.optionGroups, _renderOpts$optionPro4 = renderOpts.optionProps, optionProps = _renderOpts$optionPro4 === void 0 ? {} : _renderOpts$optionPro4, _renderOpts$optionGro3 = renderOpts.optionGroupProps, optionGroupProps = _renderOpts$optionGro3 === void 0 ? {} : _renderOpts$optionGro3;
              var groupOptions = optionGroupProps.options || "options";
              var groupLabel = optionGroupProps.label || "label";
              var column = params.column;
              var attrs = renderOpts.attrs;
              return [(0, _vue.h)("div", {
                "class": "vxe-table--filter-antd-wrapper"
              }, optionGroups ? column.filters.map(function(option, oIndex) {
                var optionValue = option.data;
                var props = getCellEditFilterProps(renderOpts, params, optionValue);
                return (0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({
                  key: oIndex
                }, attrs), props), getFilterOns(renderOpts, params, option, function() {
                  handleConfirmFilter(params, props.mode === "multiple" ? option.data && option.data.length > 0 : !_xeUtils["default"].eqNull(option.data), option);
                })), {
                  "default": function _default() {
                    return _xeUtils["default"].map(optionGroups, function(group, gIndex) {
                      return (0, _vue.h)((0, _vue.resolveComponent)("a-select-opt-group"), {
                        key: gIndex
                      }, {
                        label: function label() {
                          return (0, _vue.h)("span", {}, group[groupLabel]);
                        },
                        "default": function _default3() {
                          return renderOptions(group[groupOptions], optionProps);
                        }
                      });
                    });
                  }
                });
              }) : column.filters.map(function(option, oIndex) {
                var optionValue = option.data;
                var props = getCellEditFilterProps(renderOpts, params, optionValue);
                return (0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({
                  key: oIndex
                }, attrs), props), getFilterOns(renderOpts, params, option, function() {
                  handleConfirmFilter(params, props.mode === "multiple" ? option.data && option.data.length > 0 : !_xeUtils["default"].eqNull(option.data), option);
                })), {
                  "default": function _default() {
                    return renderOptions(options, optionProps);
                  }
                });
              }))];
            },
            defaultFilterMethod: function defaultFilterMethod(params) {
              var option = params.option, row = params.row, column = params.column;
              var data = option.data;
              var field = column.field, renderOpts = column.filterRender;
              var _renderOpts$props6 = renderOpts.props, props = _renderOpts$props6 === void 0 ? {} : _renderOpts$props6;
              var cellValue = _xeUtils["default"].get(row, field);
              if (props.mode === "multiple") {
                if (_xeUtils["default"].isArray(cellValue)) {
                  return _xeUtils["default"].includeArrays(cellValue, data);
                }
                return data.indexOf(cellValue) > -1;
              }
              return cellValue == data;
            },
            renderItemContent: function renderItemContent(renderOpts, params) {
              var _renderOpts$options5 = renderOpts.options, options = _renderOpts$options5 === void 0 ? [] : _renderOpts$options5, optionGroups = renderOpts.optionGroups, _renderOpts$optionPro5 = renderOpts.optionProps, optionProps = _renderOpts$optionPro5 === void 0 ? {} : _renderOpts$optionPro5, _renderOpts$optionGro4 = renderOpts.optionGroupProps, optionGroupProps = _renderOpts$optionGro4 === void 0 ? {} : _renderOpts$optionGro4;
              var data = params.data, field = params.field;
              var attrs = renderOpts.attrs;
              var itemValue = _xeUtils["default"].get(data, field);
              var props = getItemProps(renderOpts, params, itemValue);
              var ons = getItemOns(renderOpts, params);
              if (optionGroups) {
                var groupOptions = optionGroupProps.options || "options";
                var groupLabel = optionGroupProps.label || "label";
                return [(0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({}, attrs), props), ons), {
                  "default": function _default() {
                    return _xeUtils["default"].map(optionGroups, function(group, gIndex) {
                      return (0, _vue.h)((0, _vue.resolveComponent)("a-select-opt-group"), {
                        key: gIndex
                      }, {
                        label: function label() {
                          return (0, _vue.h)("span", {}, group[groupLabel]);
                        },
                        "default": function _default3() {
                          return renderOptions(group[groupOptions], optionProps);
                        }
                      });
                    });
                  }
                })];
              }
              return [(0, _vue.h)((0, _vue.resolveComponent)("a-select"), _objectSpread(_objectSpread(_objectSpread({}, attrs), props), ons), {
                "default": function _default() {
                  return renderOptions(options, optionProps);
                }
              })];
            },
            exportMethod: createExportMethod(getSelectCellValue)
          },
          ACascader: {
            renderEdit: createEditRender(),
            renderCell: function renderCell(renderOpts, params) {
              return getCellLabelVNs(renderOpts, params, getCascaderCellValue(renderOpts, params));
            },
            renderItemContent: createFormItemRender(),
            exportMethod: createExportMethod(getCascaderCellValue)
          },
          ADatePicker: {
            renderEdit: createEditRender(),
            renderCell: formatDatePicker("YYYY-MM-DD"),
            renderItemContent: createFormItemRender(),
            exportMethod: createDatePickerExportMethod("YYYY-MM-DD")
          },
          AMonthPicker: {
            renderEdit: createEditRender(),
            renderCell: formatDatePicker("YYYY-MM"),
            renderItemContent: createFormItemRender(),
            exportMethod: createDatePickerExportMethod("YYYY-MM")
          },
          ARangePicker: {
            renderEdit: createEditRender(),
            renderCell: function renderCell(renderOpts, params) {
              return getCellLabelVNs(renderOpts, params, getRangePickerCellValue(renderOpts, params));
            },
            renderItemContent: createFormItemRender(),
            exportMethod: createExportMethod(getRangePickerCellValue)
          },
          AWeekPicker: {
            renderEdit: createEditRender(),
            renderCell: formatDatePicker("YYYY-WW周"),
            renderItemContent: createFormItemRender(),
            exportMethod: createDatePickerExportMethod("YYYY-WW周")
          },
          ATimePicker: {
            renderEdit: createEditRender(),
            renderCell: formatDatePicker("HH:mm:ss"),
            renderItemContent: createFormItemRender(),
            exportMethod: createDatePickerExportMethod("HH:mm:ss")
          },
          ATreeSelect: {
            renderEdit: createEditRender(),
            renderCell: function renderCell(renderOpts, params) {
              return getCellLabelVNs(renderOpts, params, getTreeSelectCellValue(renderOpts, params));
            },
            renderItemContent: createFormItemRender(),
            exportMethod: createExportMethod(getTreeSelectCellValue)
          },
          ARate: {
            renderDefault: createEditRender(),
            renderEdit: createEditRender(),
            renderFilter: createFilterRender(),
            defaultFilterMethod: defaultExactFilterMethod,
            renderItemContent: createFormItemRender()
          },
          ASwitch: {
            renderDefault: createEditRender(),
            renderEdit: createEditRender(),
            renderFilter: function renderFilter(renderOpts, params) {
              var column = params.column;
              var name = renderOpts.name, attrs = renderOpts.attrs;
              return [(0, _vue.h)("div", {
                "class": "vxe-table--filter-antd-wrapper"
              }, column.filters.map(function(option, oIndex) {
                var optionValue = option.data;
                return (0, _vue.h)(name, _objectSpread(_objectSpread(_objectSpread({
                  key: oIndex
                }, attrs), getCellEditFilterProps(renderOpts, params, optionValue)), getFilterOns(renderOpts, params, option, function() {
                  handleConfirmFilter(params, _xeUtils["default"].isBoolean(option.data), option);
                })));
              }))];
            },
            defaultFilterMethod: defaultExactFilterMethod,
            renderItemContent: createFormItemRender()
          },
          ARadio: {
            renderItemContent: createFormItemRadioAndCheckboxRender()
          },
          ACheckbox: {
            renderItemContent: createFormItemRadioAndCheckboxRender()
          },
          AButton: {
            renderEdit: defaultButtonEditRender,
            renderDefault: defaultButtonEditRender,
            renderItemContent: defaultButtonItemRender
          },
          AButtons: {
            renderEdit: defaultButtonsEditRender,
            renderDefault: defaultButtonsEditRender,
            renderItemContent: defaultButtonsItemRender
          }
        });
        vxetable.interceptor.add("event.clearFilter", handleClearEvent);
        vxetable.interceptor.add("event.clearEdit", handleClearEvent);
        vxetable.interceptor.add("event.clearAreas", handleClearEvent);
        vxetable.interceptor.add("event.clearActived", handleClearEvent);
      }
    };
    if (typeof window !== "undefined" && window.VXETable && window.VXETable.use) {
      window.VXETable.use(VXETablePluginAntd);
    }
    var _default2 = exports["default"] = VXETablePluginAntd;
  }
});
export default require_index_common();
//# sourceMappingURL=vxe-table-plugin-antd.js.map
