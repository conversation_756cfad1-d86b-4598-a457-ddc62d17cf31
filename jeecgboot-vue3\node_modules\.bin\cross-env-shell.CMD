@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\src\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\src\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\src\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\src\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules\cross-env\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\cross-env@7.0.3\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\cross-env\src\bin\cross-env-shell.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\cross-env\src\bin\cross-env-shell.js" %*
)
