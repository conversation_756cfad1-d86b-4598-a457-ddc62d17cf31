<template>
  <div class="workflow-define-canvas">
    <!-- 工具栏 -->
    <WorkflowToolbar :graph="graph" :loading="loading" @save="handleSave" @add-lane="handleAddLane" @generate-json="handleGenerateJson" />

    <!-- 主要内容区域 -->
    <div class="canvas-container">
      <!-- 加载状态指示器 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载工作流数据...</div>
        </div>
      </div>

      <!-- 错误状态显示 -->
      <div v-if="!loading && !swimlaneData && route.params.id" class="error-overlay">
        <div class="error-content">
          <div class="error-icon">⚠️</div>
          <div class="error-text">工作流数据加载失败</div>
          <button class="retry-button" @click="loadData">重试</button>
        </div>
      </div>

      <!-- 左侧组件面板 -->
      <WorkflowStencil :graph="graph" :width="stencilWidth" @ready="handleStencilReady" @node-added="handleNodeAdded" />

      <!-- 中间画布区域 -->
      <WorkflowCanvas
        ref="canvasRef"
        :data="swimlaneData"
        @ready="handleCanvasReady"
        @data-change="handleDataChange"
        @selection-change="handleSelectionChange"
        @node-click="handleNodeClick"
        @edge-click="handleEdgeClick"
        @blank-click="handleBlankClick"
      />

      <!-- 右侧属性面板 -->
      <WorkflowSidebar
        :selectedNode="selectedNode"
        :selectedEdge="selectedEdge"
        :canvasData="mergedCanvasData"
        :width="sidebarWidth"
        @node-properties-change="handleNodePropertiesChange"
        @edge-properties-change="handleEdgePropertiesChange"
        @canvas-properties-change="handleCanvasPropertiesChange"
        @width-change="handleSidebarWidthChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, onUnmounted, nextTick, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Graph } from '@antv/x6';
  import { Stencil } from '@antv/x6-plugin-stencil';

  // 导入组件
  import WorkflowToolbar from './WorkflowToolbar.vue';
  import WorkflowStencil from './WorkflowStencil.vue';
  import WorkflowCanvas from './WorkflowCanvas.vue';
  import WorkflowSidebar from './WorkflowSidebar.vue';

  // 导入 API 和类型
  import { queryById, saveOrUpdate } from '../list/WorkflowDefine.api';
  import { useWorkflowNodes } from './hooks/useWorkflowNodes';
  import type { WorkflowDefineData, SwimLaneData, NodePropertyData } from './types/workflow';

  const route = useRoute();
  const { createMessage } = useMessage();
  const { createLaneNode } = useWorkflowNodes();

  // 响应式数据
  const loading = ref(false);
  const graph = ref<Graph | null>(null);
  const stencil = ref<Stencil | null>(null);
  const canvasRef = ref();

  // 布局相关
  const stencilWidth = ref(400);
  const sidebarWidth = ref(400);

  // 选中状态
  const selectedNode = ref<any>(null);
  const selectedEdge = ref<any>(null);

  // 工作流定义数据
  const data = reactive<WorkflowDefineData>({
    id: '',
    businessKey: '',
    name: '',
    definitionView: '',
    isEffective: '',
    delFlag: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    definition: '',
    nodes: '',
    startNodeId: '',
    endNodeId: '',
    edges: '',
    version: '',
  });

  // 泳道数据
  const swimlaneData = ref<SwimLaneData | null>(null);
  const canvasData = ref<any>(null);

  // 合并画布数据，包含工作流基本信息和图形数据
  const mergedCanvasData = computed(() => {
    const baseData = {
      name: data.name || '',
      businessKey: data.businessKey || '',
      version: data.version || '1.0',
      description: data.definition || '',
      id: data.id,
      isEffective: data.isEffective,
      createTime: data.createTime,
      updateTime: data.updateTime,
      nodeCount: 0,
      edgeCount: 0,
      laneCount: 0,
    };

    // 如果有画布数据，合并统计信息
    if (canvasData.value) {
      return {
        ...baseData,
        ...canvasData.value,
      };
    }

    // 如果有泳道数据，从中计算统计信息
    if (swimlaneData.value) {
      const { lanes = [], nodes = [], edges = [] } = swimlaneData.value;
      return {
        ...baseData,
        nodeCount: nodes.length,
        edgeCount: edges.length,
        laneCount: lanes.length,
        lanes,
        nodes,
        edges,
        cells: [...lanes, ...nodes, ...edges],
      };
    }

    return baseData;
  });

  /**
   * 画布准备就绪事件
   */
  const handleCanvasReady = (graphInstance: Graph) => {
    graph.value = graphInstance;

    // 确保在graph实例设置后，组件面板能够正确初始化
    nextTick(() => {
      // Graph实例已设置，Stencil将自动初始化
    });
  };

  /**
   * 组件面板准备就绪事件
   */
  const handleStencilReady = (stencilInstance: Stencil) => {
    stencil.value = stencilInstance;
  };

  /**
   * 数据变化事件
   */
  const handleDataChange = (graphData: any) => {
    if (graphData && graphData.cells) {
      // 从cells中计算统计信息
      const cells = graphData.cells || [];
      const nodes = cells.filter((cell: any) => cell.shape && !cell.source && !cell.target);
      const edges = cells.filter((cell: any) => cell.source && cell.target);
      const lanes = cells.filter((cell: any) => cell.shape === 'lane');

      canvasData.value = {
        ...graphData,
        nodeCount: nodes.length,
        edgeCount: edges.length,
        laneCount: lanes.length,
        nodes,
        edges,
        lanes,
      };
    }
  };

  /**
   * 选择变化事件
   */
  const handleSelectionChange = (_selection: { nodes: any[]; edges: any[] }) => {
    // 处理选择变化逻辑
  };

  /**
   * 节点点击事件
   */
  const handleNodeClick = (node: any) => {
    selectedNode.value = node;
    selectedEdge.value = null;
  };

  /**
   * 边点击事件
   */
  const handleEdgeClick = (edge: any) => {
    selectedEdge.value = edge;
    selectedNode.value = null;
  };

  /**
   * 空白点击事件
   */
  const handleBlankClick = () => {
    selectedNode.value = null;
    selectedEdge.value = null;
  };

  /**
   * 节点添加事件
   */
  const handleNodeAdded = (_nodeData: any) => {
    // 处理节点添加逻辑
  };

  /**
   * 节点属性变化事件
   */
  const handleNodePropertiesChange = (properties: NodePropertyData) => {
    if (canvasRef.value && selectedNode.value) {
      canvasRef.value.updateSelectedNodeData(properties);
    }
  };

  /**
   * 边属性变化事件
   */
  const handleEdgePropertiesChange = (properties: any) => {
    if (canvasRef.value && selectedEdge.value) {
      canvasRef.value.updateSelectedEdgeData(properties);
    }
  };

  /**
   * 画布属性变化事件
   */
  const handleCanvasPropertiesChange = (properties: any) => {
    Object.assign(data, properties);
  };

  /**
   * 侧边栏宽度变化事件
   */
  const handleSidebarWidthChange = (width: number) => {
    sidebarWidth.value = width;
  };

  /**
   * 保存工作流
   */
  const handleSave = async () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    loading.value = true;
    try {
      // 获取画布数据
      const graphData = canvasRef.value?.getData();
      if (graphData) {
        // 更新定义视图数据
        data.definitionView = JSON.stringify({
          lanes: graphData.cells?.filter((cell: any) => cell.shape === 'lane') || [],
          nodes: graphData.cells?.filter((cell: any) => cell.shape && cell.shape !== 'lane' && !cell.source && !cell.target) || [],
          edges: graphData.cells?.filter((cell: any) => cell.source && cell.target) || [],
        });

        // 调用保存 API
        const isUpdate = !!data.id;
        const result = await saveOrUpdate(data, isUpdate);

        if (result.success) {
          createMessage.success('保存成功');
        } else {
          createMessage.error(result.message || '保存失败');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      loading.value = false;
    }
  };

  /**
   * 添加泳道
   */
  const handleAddLane = () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    try {
      createLaneNode(graph.value as Graph);
      createMessage.success('泳道添加成功');
    } catch (error) {
      console.error('添加泳道失败:', error);
      createMessage.error('添加泳道失败');
    }
  };

  /**
   * 生成JSON
   */
  const handleGenerateJson = () => {
    if (!graph.value) {
      createMessage.warning('图形实例未初始化');
      return;
    }

    try {
      const graphData = canvasRef.value?.getData();
      if (graphData) {
        const exportData = {
          nodes: graphData.cells?.filter((cell: any) => cell.shape && !cell.source && !cell.target) || [],
          edges: graphData.cells?.filter((cell: any) => cell.source && cell.target) || [],
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        console.log('导出的JSON数据：', jsonString);
        createMessage.success('JSON数据已输出到控制台');
      }
    } catch (error) {
      console.error('生成JSON失败:', error);
      createMessage.error('生成JSON失败');
    }
  };
  /**
   * 加载工作流数据
   */
  const loadData = async () => {
    if (!route.params.id) {
      return;
    }

    loading.value = true;
    try {
      const res = await queryById({ id: route.params.id });

      if (res) {
        Object.assign(data, res);

        // 解析定义视图数据
        if (data.definitionView) {
          try {
            const parsedData = JSON.parse(data.definitionView);
            swimlaneData.value = parsedData;
          } catch (error) {
            createMessage.error('工作流数据格式错误');
          }
        } else {
          // 创建默认的空泳道数据结构
          swimlaneData.value = {
            lanes: [],
            nodes: [],
            edges: [],
          };
        }
      } else {
        createMessage.warning('未找到工作流数据');
      }
    } catch (error) {
      createMessage.error('加载工作流数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 生命周期函数
  onMounted(async () => {
    // 延迟加载数据，确保DOM完全渲染
    await nextTick();
    loadData();
  });

  onUnmounted(() => {
    // 清理资源，防止内存泄漏
    try {
      if (graph.value) {
        // 清理Graph实例
        graph.value.dispose?.();
        graph.value = null;
      }
      if (stencil.value) {
        // 清理Stencil实例
        stencil.value.dispose?.();
        stencil.value = null;
      }

      // 清理数据引用
      swimlaneData.value = null;
      canvasData.value = null;
      selectedNode.value = null;
      selectedEdge.value = null;
    } catch (error) {
      // 清理过程中的错误不影响组件卸载
    }
  });
</script>

<style lang="less" scoped>
  .workflow-define-canvas {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: calc(100vh - 85px - 40px);
    background-color: #ffffff;
    position: relative;

    .canvas-container {
      display: flex;
      flex: 1;
      height: 100%;
      border: 1px solid #dfe3e8;
      border-top: none;
      overflow: hidden;
      position: relative;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .loading-content {
      text-align: center;
      padding: 20px;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 16px;
    }

    .loading-text {
      color: #666;
      font-size: 14px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .error-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }

    .error-content {
      text-align: center;
      padding: 20px;
    }

    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .error-text {
      color: #ff4d4f;
      font-size: 16px;
      margin-bottom: 16px;
    }

    .retry-button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background-color: #40a9ff;
      }
    }
  }
</style>
