#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules/which/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules/which/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules/which/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules/which/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/which@1.3.1/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../which/bin/which" "$@"
else
  exec node  "$basedir/../which/bin/which" "$@"
fi
