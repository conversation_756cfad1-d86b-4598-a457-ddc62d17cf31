#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules/stylelint-config-prettier/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules/stylelint-config-prettier/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules/stylelint-config-prettier/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules/stylelint-config-prettier/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/stylelint-config-prettier@9.0.5_stylelint@16.22.0/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../stylelint-config-prettier/bin/check.js" "$@"
else
  exec node  "$basedir/../stylelint-config-prettier/bin/check.js" "$@"
fi
