{"version": 3, "sources": ["../../.pnpm/@ant-design+fast-color@2.0.6/node_modules/@ant-design/fast-color/es/FastColor.js", "../../.pnpm/@ant-design+colors@7.2.1/node_modules/@ant-design/colors/es/generate.js", "../../.pnpm/@ant-design+colors@7.2.1/node_modules/@ant-design/colors/es/presets.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nconst round = Math.round;\n\n/**\n * Support format, alpha unit will check the % mark:\n * - rgba(102, 204, 255, .5)      -> [102, 204, 255, 0.5]\n * - rgb(102 204 255 / .5)        -> [102, 204, 255, 0.5]\n * - rgb(100%, 50%, 0% / 50%)     -> [255, 128, 0, 0.5]\n * - hsl(270, 60, 40, .5)         -> [270, 60, 40, 0.5]\n * - hsl(270deg 60% 40% / 50%)   -> [270, 60, 40, 0.5]\n *\n * When `base` is provided, the percentage value will be divided by `base`.\n */\nfunction splitColorStr(str, parseNum) {\n  const match = str\n  // Remove str before `(`\n  .replace(/^[^(]*\\((.*)/, '$1')\n  // Remove str after `)`\n  .replace(/\\).*/, '').match(/\\d*\\.?\\d+%?/g) || [];\n  const numList = match.map(item => parseFloat(item));\n  for (let i = 0; i < 3; i += 1) {\n    numList[i] = parseNum(numList[i] || 0, match[i] || '', i);\n  }\n\n  // For alpha. 50% should be 0.5\n  if (match[3]) {\n    numList[3] = match[3].includes('%') ? numList[3] / 100 : numList[3];\n  } else {\n    // By default, alpha is 1\n    numList[3] = 1;\n  }\n  return numList;\n}\nconst parseHSVorHSL = (num, _, index) => index === 0 ? num : num / 100;\n\n/** round and limit number to integer between 0-255 */\nfunction limitRange(value, max) {\n  const mergedMax = max || 255;\n  if (value > mergedMax) {\n    return mergedMax;\n  }\n  if (value < 0) {\n    return 0;\n  }\n  return value;\n}\nexport class FastColor {\n  constructor(input) {\n    /**\n     * All FastColor objects are valid. So isValid is always true. This property is kept to be compatible with TinyColor.\n     */\n    _defineProperty(this, \"isValid\", true);\n    /**\n     * Red, R in RGB\n     */\n    _defineProperty(this, \"r\", 0);\n    /**\n     * Green, G in RGB\n     */\n    _defineProperty(this, \"g\", 0);\n    /**\n     * Blue, B in RGB\n     */\n    _defineProperty(this, \"b\", 0);\n    /**\n     * Alpha/Opacity, A in RGBA/HSLA\n     */\n    _defineProperty(this, \"a\", 1);\n    // HSV privates\n    _defineProperty(this, \"_h\", void 0);\n    _defineProperty(this, \"_s\", void 0);\n    _defineProperty(this, \"_l\", void 0);\n    _defineProperty(this, \"_v\", void 0);\n    // intermediate variables to calculate HSL/HSV\n    _defineProperty(this, \"_max\", void 0);\n    _defineProperty(this, \"_min\", void 0);\n    _defineProperty(this, \"_brightness\", void 0);\n    /**\n     * Always check 3 char in the object to determine the format.\n     * We not use function in check to save bundle size.\n     * e.g. 'rgb' -> { r: 0, g: 0, b: 0 }.\n     */\n    function matchFormat(str) {\n      return str[0] in input && str[1] in input && str[2] in input;\n    }\n    if (!input) {\n      // Do nothing since already initialized\n    } else if (typeof input === 'string') {\n      const trimStr = input.trim();\n      function matchPrefix(prefix) {\n        return trimStr.startsWith(prefix);\n      }\n      if (/^#?[A-F\\d]{3,8}$/i.test(trimStr)) {\n        this.fromHexString(trimStr);\n      } else if (matchPrefix('rgb')) {\n        this.fromRgbString(trimStr);\n      } else if (matchPrefix('hsl')) {\n        this.fromHslString(trimStr);\n      } else if (matchPrefix('hsv') || matchPrefix('hsb')) {\n        this.fromHsvString(trimStr);\n      }\n    } else if (input instanceof FastColor) {\n      this.r = input.r;\n      this.g = input.g;\n      this.b = input.b;\n      this.a = input.a;\n      this._h = input._h;\n      this._s = input._s;\n      this._l = input._l;\n      this._v = input._v;\n    } else if (matchFormat('rgb')) {\n      this.r = limitRange(input.r);\n      this.g = limitRange(input.g);\n      this.b = limitRange(input.b);\n      this.a = typeof input.a === 'number' ? limitRange(input.a, 1) : 1;\n    } else if (matchFormat('hsl')) {\n      this.fromHsl(input);\n    } else if (matchFormat('hsv')) {\n      this.fromHsv(input);\n    } else {\n      throw new Error('@ant-design/fast-color: unsupported input ' + JSON.stringify(input));\n    }\n  }\n\n  // ======================= Setter =======================\n\n  setR(value) {\n    return this._sc('r', value);\n  }\n  setG(value) {\n    return this._sc('g', value);\n  }\n  setB(value) {\n    return this._sc('b', value);\n  }\n  setA(value) {\n    return this._sc('a', value, 1);\n  }\n  setHue(value) {\n    const hsv = this.toHsv();\n    hsv.h = value;\n    return this._c(hsv);\n  }\n\n  // ======================= Getter =======================\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   * @see http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n   */\n  getLuminance() {\n    function adjustGamma(raw) {\n      const val = raw / 255;\n      return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);\n    }\n    const R = adjustGamma(this.r);\n    const G = adjustGamma(this.g);\n    const B = adjustGamma(this.b);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  getHue() {\n    if (typeof this._h === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._h = 0;\n      } else {\n        this._h = round(60 * (this.r === this.getMax() ? (this.g - this.b) / delta + (this.g < this.b ? 6 : 0) : this.g === this.getMax() ? (this.b - this.r) / delta + 2 : (this.r - this.g) / delta + 4));\n      }\n    }\n    return this._h;\n  }\n  getSaturation() {\n    if (typeof this._s === 'undefined') {\n      const delta = this.getMax() - this.getMin();\n      if (delta === 0) {\n        this._s = 0;\n      } else {\n        this._s = delta / this.getMax();\n      }\n    }\n    return this._s;\n  }\n  getLightness() {\n    if (typeof this._l === 'undefined') {\n      this._l = (this.getMax() + this.getMin()) / 510;\n    }\n    return this._l;\n  }\n  getValue() {\n    if (typeof this._v === 'undefined') {\n      this._v = this.getMax() / 255;\n    }\n    return this._v;\n  }\n\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   * Note: this is not the b of HSB\n   * @see http://www.w3.org/TR/AERT#color-contrast\n   */\n  getBrightness() {\n    if (typeof this._brightness === 'undefined') {\n      this._brightness = (this.r * 299 + this.g * 587 + this.b * 114) / 1000;\n    }\n    return this._brightness;\n  }\n\n  // ======================== Func ========================\n\n  darken(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() - amount / 100;\n    if (l < 0) {\n      l = 0;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n  lighten(amount = 10) {\n    const h = this.getHue();\n    const s = this.getSaturation();\n    let l = this.getLightness() + amount / 100;\n    if (l > 1) {\n      l = 1;\n    }\n    return this._c({\n      h,\n      s,\n      l,\n      a: this.a\n    });\n  }\n\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(input, amount = 50) {\n    const color = this._c(input);\n    const p = amount / 100;\n    const calc = key => (color[key] - this[key]) * p + this[key];\n    const rgba = {\n      r: round(calc('r')),\n      g: round(calc('g')),\n      b: round(calc('b')),\n      a: round(calc('a') * 100) / 100\n    };\n    return this._c(rgba);\n  }\n\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   */\n  tint(amount = 10) {\n    return this.mix({\n      r: 255,\n      g: 255,\n      b: 255,\n      a: 1\n    }, amount);\n  }\n\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   */\n  shade(amount = 10) {\n    return this.mix({\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    }, amount);\n  }\n  onBackground(background) {\n    const bg = this._c(background);\n    const alpha = this.a + bg.a * (1 - this.a);\n    const calc = key => {\n      return round((this[key] * this.a + bg[key] * bg.a * (1 - this.a)) / alpha);\n    };\n    return this._c({\n      r: calc('r'),\n      g: calc('g'),\n      b: calc('b'),\n      a: alpha\n    });\n  }\n\n  // ======================= Status =======================\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return this.getBrightness() >= 128;\n  }\n\n  // ======================== MISC ========================\n  equals(other) {\n    return this.r === other.r && this.g === other.g && this.b === other.b && this.a === other.a;\n  }\n  clone() {\n    return this._c(this);\n  }\n\n  // ======================= Format =======================\n  toHexString() {\n    let hex = '#';\n    const rHex = (this.r || 0).toString(16);\n    hex += rHex.length === 2 ? rHex : '0' + rHex;\n    const gHex = (this.g || 0).toString(16);\n    hex += gHex.length === 2 ? gHex : '0' + gHex;\n    const bHex = (this.b || 0).toString(16);\n    hex += bHex.length === 2 ? bHex : '0' + bHex;\n    if (typeof this.a === 'number' && this.a >= 0 && this.a < 1) {\n      const aHex = round(this.a * 255).toString(16);\n      hex += aHex.length === 2 ? aHex : '0' + aHex;\n    }\n    return hex;\n  }\n\n  /** CSS support color pattern */\n  toHsl() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      l: this.getLightness(),\n      a: this.a\n    };\n  }\n\n  /** CSS support color pattern */\n  toHslString() {\n    const h = this.getHue();\n    const s = round(this.getSaturation() * 100);\n    const l = round(this.getLightness() * 100);\n    return this.a !== 1 ? `hsla(${h},${s}%,${l}%,${this.a})` : `hsl(${h},${s}%,${l}%)`;\n  }\n\n  /** Same as toHsb */\n  toHsv() {\n    return {\n      h: this.getHue(),\n      s: this.getSaturation(),\n      v: this.getValue(),\n      a: this.a\n    };\n  }\n  toRgb() {\n    return {\n      r: this.r,\n      g: this.g,\n      b: this.b,\n      a: this.a\n    };\n  }\n  toRgbString() {\n    return this.a !== 1 ? `rgba(${this.r},${this.g},${this.b},${this.a})` : `rgb(${this.r},${this.g},${this.b})`;\n  }\n  toString() {\n    return this.toRgbString();\n  }\n\n  // ====================== Privates ======================\n  /** Return a new FastColor object with one channel changed */\n  _sc(rgb, value, max) {\n    const clone = this.clone();\n    clone[rgb] = limitRange(value, max);\n    return clone;\n  }\n  _c(input) {\n    return new this.constructor(input);\n  }\n  getMax() {\n    if (typeof this._max === 'undefined') {\n      this._max = Math.max(this.r, this.g, this.b);\n    }\n    return this._max;\n  }\n  getMin() {\n    if (typeof this._min === 'undefined') {\n      this._min = Math.min(this.r, this.g, this.b);\n    }\n    return this._min;\n  }\n  fromHexString(trimStr) {\n    const withoutPrefix = trimStr.replace('#', '');\n    function connectNum(index1, index2) {\n      return parseInt(withoutPrefix[index1] + withoutPrefix[index2 || index1], 16);\n    }\n    if (withoutPrefix.length < 6) {\n      // #rgb or #rgba\n      this.r = connectNum(0);\n      this.g = connectNum(1);\n      this.b = connectNum(2);\n      this.a = withoutPrefix[3] ? connectNum(3) / 255 : 1;\n    } else {\n      // #rrggbb or #rrggbbaa\n      this.r = connectNum(0, 1);\n      this.g = connectNum(2, 3);\n      this.b = connectNum(4, 5);\n      this.a = withoutPrefix[6] ? connectNum(6, 7) / 255 : 1;\n    }\n  }\n  fromHsl({\n    h,\n    s,\n    l,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._l = l;\n    this.a = typeof a === 'number' ? a : 1;\n    if (s <= 0) {\n      const rgb = round(l * 255);\n      this.r = rgb;\n      this.g = rgb;\n      this.b = rgb;\n    }\n    let r = 0,\n      g = 0,\n      b = 0;\n    const huePrime = h / 60;\n    const chroma = (1 - Math.abs(2 * l - 1)) * s;\n    const secondComponent = chroma * (1 - Math.abs(huePrime % 2 - 1));\n    if (huePrime >= 0 && huePrime < 1) {\n      r = chroma;\n      g = secondComponent;\n    } else if (huePrime >= 1 && huePrime < 2) {\n      r = secondComponent;\n      g = chroma;\n    } else if (huePrime >= 2 && huePrime < 3) {\n      g = chroma;\n      b = secondComponent;\n    } else if (huePrime >= 3 && huePrime < 4) {\n      g = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 4 && huePrime < 5) {\n      r = secondComponent;\n      b = chroma;\n    } else if (huePrime >= 5 && huePrime < 6) {\n      r = chroma;\n      b = secondComponent;\n    }\n    const lightnessModification = l - chroma / 2;\n    this.r = round((r + lightnessModification) * 255);\n    this.g = round((g + lightnessModification) * 255);\n    this.b = round((b + lightnessModification) * 255);\n  }\n  fromHsv({\n    h,\n    s,\n    v,\n    a\n  }) {\n    this._h = h % 360;\n    this._s = s;\n    this._v = v;\n    this.a = typeof a === 'number' ? a : 1;\n    const vv = round(v * 255);\n    this.r = vv;\n    this.g = vv;\n    this.b = vv;\n    if (s <= 0) {\n      return;\n    }\n    const hh = h / 60;\n    const i = Math.floor(hh);\n    const ff = hh - i;\n    const p = round(v * (1.0 - s) * 255);\n    const q = round(v * (1.0 - s * ff) * 255);\n    const t = round(v * (1.0 - s * (1.0 - ff)) * 255);\n    switch (i) {\n      case 0:\n        this.g = t;\n        this.b = p;\n        break;\n      case 1:\n        this.r = q;\n        this.b = p;\n        break;\n      case 2:\n        this.r = p;\n        this.b = t;\n        break;\n      case 3:\n        this.r = p;\n        this.g = q;\n        break;\n      case 4:\n        this.r = t;\n        this.g = p;\n        break;\n      case 5:\n      default:\n        this.g = p;\n        this.b = q;\n        break;\n    }\n  }\n  fromHsvString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsv({\n      h: cells[0],\n      s: cells[1],\n      v: cells[2],\n      a: cells[3]\n    });\n  }\n  fromHslString(trimStr) {\n    const cells = splitColorStr(trimStr, parseHSVorHSL);\n    this.fromHsl({\n      h: cells[0],\n      s: cells[1],\n      l: cells[2],\n      a: cells[3]\n    });\n  }\n  fromRgbString(trimStr) {\n    const cells = splitColorStr(trimStr, (num, txt) =>\n    // Convert percentage to number. e.g. 50% -> 128\n    txt.includes('%') ? round(num / 100 * 255) : num);\n    this.r = cells[0];\n    this.g = cells[1];\n    this.b = cells[2];\n    this.a = cells[3];\n  }\n}", "import { FastColor } from '@ant-design/fast-color';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  amount: 15\n}, {\n  index: 6,\n  amount: 25\n}, {\n  index: 5,\n  amount: 30\n}, {\n  index: 5,\n  amount: 45\n}, {\n  index: 5,\n  amount: 65\n}, {\n  index: 5,\n  amount: 85\n}, {\n  index: 4,\n  amount: 90\n}, {\n  index: 3,\n  amount: 95\n}, {\n  index: 2,\n  amount: 97\n}, {\n  index: 1,\n  amount: 98\n}];\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Math.round(saturation * 100) / 100;\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  // Clamp value between 0 and 1\n  value = Math.max(0, Math.min(1, value));\n  return Math.round(value * 100) / 100;\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = new FastColor(color);\n  var hsv = pColor.toHsv();\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var c = new FastColor({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    });\n    patterns.push(c);\n  }\n  patterns.push(pColor);\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _c = new FastColor({\n      h: getHue(hsv, _i),\n      s: getSaturation(hsv, _i),\n      v: getValue(hsv, _i)\n    });\n    patterns.push(_c);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref) {\n      var index = _ref.index,\n        amount = _ref.amount;\n      return new FastColor(opts.backgroundColor || '#141414').mix(patterns[index], amount).toHexString();\n    });\n  }\n  return patterns.map(function (c) {\n    return c.toHexString();\n  });\n}", "// Generated by script. Do NOT modify!\n\nexport var presetPrimaryColors = {\n  \"red\": \"#F5222D\",\n  \"volcano\": \"#FA541C\",\n  \"orange\": \"#FA8C16\",\n  \"gold\": \"#FAAD14\",\n  \"yellow\": \"#FADB14\",\n  \"lime\": \"#A0D911\",\n  \"green\": \"#52C41A\",\n  \"cyan\": \"#13C2C2\",\n  \"blue\": \"#1677FF\",\n  \"geekblue\": \"#2F54EB\",\n  \"purple\": \"#722ED1\",\n  \"magenta\": \"#EB2F96\",\n  \"grey\": \"#666666\"\n};\nexport var red = [\"#fff1f0\", \"#ffccc7\", \"#ffa39e\", \"#ff7875\", \"#ff4d4f\", \"#f5222d\", \"#cf1322\", \"#a8071a\", \"#820014\", \"#5c0011\"];\nred.primary = red[5];\nexport var volcano = [\"#fff2e8\", \"#ffd8bf\", \"#ffbb96\", \"#ff9c6e\", \"#ff7a45\", \"#fa541c\", \"#d4380d\", \"#ad2102\", \"#871400\", \"#610b00\"];\nvolcano.primary = volcano[5];\nexport var orange = [\"#fff7e6\", \"#ffe7ba\", \"#ffd591\", \"#ffc069\", \"#ffa940\", \"#fa8c16\", \"#d46b08\", \"#ad4e00\", \"#873800\", \"#612500\"];\norange.primary = orange[5];\nexport var gold = [\"#fffbe6\", \"#fff1b8\", \"#ffe58f\", \"#ffd666\", \"#ffc53d\", \"#faad14\", \"#d48806\", \"#ad6800\", \"#874d00\", \"#613400\"];\ngold.primary = gold[5];\nexport var yellow = [\"#feffe6\", \"#ffffb8\", \"#fffb8f\", \"#fff566\", \"#ffec3d\", \"#fadb14\", \"#d4b106\", \"#ad8b00\", \"#876800\", \"#614700\"];\nyellow.primary = yellow[5];\nexport var lime = [\"#fcffe6\", \"#f4ffb8\", \"#eaff8f\", \"#d3f261\", \"#bae637\", \"#a0d911\", \"#7cb305\", \"#5b8c00\", \"#3f6600\", \"#254000\"];\nlime.primary = lime[5];\nexport var green = [\"#f6ffed\", \"#d9f7be\", \"#b7eb8f\", \"#95de64\", \"#73d13d\", \"#52c41a\", \"#389e0d\", \"#237804\", \"#135200\", \"#092b00\"];\ngreen.primary = green[5];\nexport var cyan = [\"#e6fffb\", \"#b5f5ec\", \"#87e8de\", \"#5cdbd3\", \"#36cfc9\", \"#13c2c2\", \"#08979c\", \"#006d75\", \"#00474f\", \"#002329\"];\ncyan.primary = cyan[5];\nexport var blue = [\"#e6f4ff\", \"#bae0ff\", \"#91caff\", \"#69b1ff\", \"#4096ff\", \"#1677ff\", \"#0958d9\", \"#003eb3\", \"#002c8c\", \"#001d66\"];\nblue.primary = blue[5];\nexport var geekblue = [\"#f0f5ff\", \"#d6e4ff\", \"#adc6ff\", \"#85a5ff\", \"#597ef7\", \"#2f54eb\", \"#1d39c4\", \"#10239e\", \"#061178\", \"#030852\"];\ngeekblue.primary = geekblue[5];\nexport var purple = [\"#f9f0ff\", \"#efdbff\", \"#d3adf7\", \"#b37feb\", \"#9254de\", \"#722ed1\", \"#531dab\", \"#391085\", \"#22075e\", \"#120338\"];\npurple.primary = purple[5];\nexport var magenta = [\"#fff0f6\", \"#ffd6e7\", \"#ffadd2\", \"#ff85c0\", \"#f759ab\", \"#eb2f96\", \"#c41d7f\", \"#9e1068\", \"#780650\", \"#520339\"];\nmagenta.primary = magenta[5];\nexport var grey = [\"#a6a6a6\", \"#999999\", \"#8c8c8c\", \"#808080\", \"#737373\", \"#666666\", \"#404040\", \"#1a1a1a\", \"#000000\", \"#000000\"];\ngrey.primary = grey[5];\nexport var gray = grey;\nexport var presetPalettes = {\n  red: red,\n  volcano: volcano,\n  orange: orange,\n  gold: gold,\n  yellow: yellow,\n  lime: lime,\n  green: green,\n  cyan: cyan,\n  blue: blue,\n  geekblue: geekblue,\n  purple: purple,\n  magenta: magenta,\n  grey: grey\n};\nexport var redDark = [\"#2a1215\", \"#431418\", \"#58181c\", \"#791a1f\", \"#a61d24\", \"#d32029\", \"#e84749\", \"#f37370\", \"#f89f9a\", \"#fac8c3\"];\nredDark.primary = redDark[5];\nexport var volcanoDark = [\"#2b1611\", \"#441d12\", \"#592716\", \"#7c3118\", \"#aa3e19\", \"#d84a1b\", \"#e87040\", \"#f3956a\", \"#f8b692\", \"#fad4bc\"];\nvolcanoDark.primary = volcanoDark[5];\nexport var orangeDark = [\"#2b1d11\", \"#442a11\", \"#593815\", \"#7c4a15\", \"#aa6215\", \"#d87a16\", \"#e89a3c\", \"#f3b765\", \"#f8cf8d\", \"#fae3b7\"];\norangeDark.primary = orangeDark[5];\nexport var goldDark = [\"#2b2111\", \"#443111\", \"#594214\", \"#7c5914\", \"#aa7714\", \"#d89614\", \"#e8b339\", \"#f3cc62\", \"#f8df8b\", \"#faedb5\"];\ngoldDark.primary = goldDark[5];\nexport var yellowDark = [\"#2b2611\", \"#443b11\", \"#595014\", \"#7c6e14\", \"#aa9514\", \"#d8bd14\", \"#e8d639\", \"#f3ea62\", \"#f8f48b\", \"#fafab5\"];\nyellowDark.primary = yellowDark[5];\nexport var limeDark = [\"#1f2611\", \"#2e3c10\", \"#3e4f13\", \"#536d13\", \"#6f9412\", \"#8bbb11\", \"#a9d134\", \"#c9e75d\", \"#e4f88b\", \"#f0fab5\"];\nlimeDark.primary = limeDark[5];\nexport var greenDark = [\"#162312\", \"#1d3712\", \"#274916\", \"#306317\", \"#3c8618\", \"#49aa19\", \"#6abe39\", \"#8fd460\", \"#b2e58b\", \"#d5f2bb\"];\ngreenDark.primary = greenDark[5];\nexport var cyanDark = [\"#112123\", \"#113536\", \"#144848\", \"#146262\", \"#138585\", \"#13a8a8\", \"#33bcb7\", \"#58d1c9\", \"#84e2d8\", \"#b2f1e8\"];\ncyanDark.primary = cyanDark[5];\nexport var blueDark = [\"#111a2c\", \"#112545\", \"#15325b\", \"#15417e\", \"#1554ad\", \"#1668dc\", \"#3c89e8\", \"#65a9f3\", \"#8dc5f8\", \"#b7dcfa\"];\nblueDark.primary = blueDark[5];\nexport var geekblueDark = [\"#131629\", \"#161d40\", \"#1c2755\", \"#203175\", \"#263ea0\", \"#2b4acb\", \"#5273e0\", \"#7f9ef3\", \"#a8c1f8\", \"#d2e0fa\"];\ngeekblueDark.primary = geekblueDark[5];\nexport var purpleDark = [\"#1a1325\", \"#24163a\", \"#301c4d\", \"#3e2069\", \"#51258f\", \"#642ab5\", \"#854eca\", \"#ab7ae0\", \"#cda8f0\", \"#ebd7fa\"];\npurpleDark.primary = purpleDark[5];\nexport var magentaDark = [\"#291321\", \"#40162f\", \"#551c3b\", \"#75204f\", \"#a02669\", \"#cb2b83\", \"#e0529c\", \"#f37fb7\", \"#f8a8cc\", \"#fad2e3\"];\nmagentaDark.primary = magentaDark[5];\nexport var greyDark = [\"#151515\", \"#1f1f1f\", \"#2d2d2d\", \"#393939\", \"#494949\", \"#5a5a5a\", \"#6a6a6a\", \"#7b7b7b\", \"#888888\", \"#969696\"];\ngreyDark.primary = greyDark[5];\nexport var presetDarkPalettes = {\n  red: redDark,\n  volcano: volcanoDark,\n  orange: orangeDark,\n  gold: goldDark,\n  yellow: yellowDark,\n  lime: limeDark,\n  green: greenDark,\n  cyan: cyanDark,\n  blue: blueDark,\n  geekblue: geekblueDark,\n  purple: purpleDark,\n  magenta: magentaDark,\n  grey: greyDark\n};"], "mappings": ";;;;;;AACA,IAAM,QAAQ,KAAK;AAYnB,SAAS,cAAc,KAAK,UAAU;AACpC,QAAM,QAAQ,IAEb,QAAQ,gBAAgB,IAAI,EAE5B,QAAQ,QAAQ,EAAE,EAAE,MAAM,cAAc,KAAK,CAAC;AAC/C,QAAM,UAAU,MAAM,IAAI,UAAQ,WAAW,IAAI,CAAC;AAClD,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,YAAQ,CAAC,IAAI,SAAS,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC;AAAA,EAC1D;AAGA,MAAI,MAAM,CAAC,GAAG;AACZ,YAAQ,CAAC,IAAI,MAAM,CAAC,EAAE,SAAS,GAAG,IAAI,QAAQ,CAAC,IAAI,MAAM,QAAQ,CAAC;AAAA,EACpE,OAAO;AAEL,YAAQ,CAAC,IAAI;AAAA,EACf;AACA,SAAO;AACT;AACA,IAAM,gBAAgB,CAAC,KAAK,GAAG,UAAU,UAAU,IAAI,MAAM,MAAM;AAGnE,SAAS,WAAW,OAAO,KAAK;AAC9B,QAAM,YAAY,OAAO;AACzB,MAAI,QAAQ,WAAW;AACrB,WAAO;AAAA,EACT;AACA,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACO,IAAM,YAAN,MAAM,WAAU;AAAA,EACrB,YAAY,OAAO;AAIjB,oBAAgB,MAAM,WAAW,IAAI;AAIrC,oBAAgB,MAAM,KAAK,CAAC;AAI5B,oBAAgB,MAAM,KAAK,CAAC;AAI5B,oBAAgB,MAAM,KAAK,CAAC;AAI5B,oBAAgB,MAAM,KAAK,CAAC;AAE5B,oBAAgB,MAAM,MAAM,MAAM;AAClC,oBAAgB,MAAM,MAAM,MAAM;AAClC,oBAAgB,MAAM,MAAM,MAAM;AAClC,oBAAgB,MAAM,MAAM,MAAM;AAElC,oBAAgB,MAAM,QAAQ,MAAM;AACpC,oBAAgB,MAAM,QAAQ,MAAM;AACpC,oBAAgB,MAAM,eAAe,MAAM;AAM3C,aAAS,YAAY,KAAK;AACxB,aAAO,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK;AAAA,IACzD;AACA,QAAI,CAAC,OAAO;AAAA,IAEZ,WAAW,OAAO,UAAU,UAAU;AAEpC,UAAS,cAAT,SAAqB,QAAQ;AAC3B,eAAO,QAAQ,WAAW,MAAM;AAAA,MAClC;AAHA,YAAM,UAAU,MAAM,KAAK;AAI3B,UAAI,oBAAoB,KAAK,OAAO,GAAG;AACrC,aAAK,cAAc,OAAO;AAAA,MAC5B,WAAW,YAAY,KAAK,GAAG;AAC7B,aAAK,cAAc,OAAO;AAAA,MAC5B,WAAW,YAAY,KAAK,GAAG;AAC7B,aAAK,cAAc,OAAO;AAAA,MAC5B,WAAW,YAAY,KAAK,KAAK,YAAY,KAAK,GAAG;AACnD,aAAK,cAAc,OAAO;AAAA,MAC5B;AAAA,IACF,WAAW,iBAAiB,YAAW;AACrC,WAAK,IAAI,MAAM;AACf,WAAK,IAAI,MAAM;AACf,WAAK,IAAI,MAAM;AACf,WAAK,IAAI,MAAM;AACf,WAAK,KAAK,MAAM;AAChB,WAAK,KAAK,MAAM;AAChB,WAAK,KAAK,MAAM;AAChB,WAAK,KAAK,MAAM;AAAA,IAClB,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAK,IAAI,WAAW,MAAM,CAAC;AAC3B,WAAK,IAAI,WAAW,MAAM,CAAC;AAC3B,WAAK,IAAI,WAAW,MAAM,CAAC;AAC3B,WAAK,IAAI,OAAO,MAAM,MAAM,WAAW,WAAW,MAAM,GAAG,CAAC,IAAI;AAAA,IAClE,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAK,QAAQ,KAAK;AAAA,IACpB,WAAW,YAAY,KAAK,GAAG;AAC7B,WAAK,QAAQ,KAAK;AAAA,IACpB,OAAO;AACL,YAAM,IAAI,MAAM,+CAA+C,KAAK,UAAU,KAAK,CAAC;AAAA,IACtF;AAAA,EACF;AAAA;AAAA,EAIA,KAAK,OAAO;AACV,WAAO,KAAK,IAAI,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,KAAK,OAAO;AACV,WAAO,KAAK,IAAI,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,KAAK,OAAO;AACV,WAAO,KAAK,IAAI,KAAK,KAAK;AAAA,EAC5B;AAAA,EACA,KAAK,OAAO;AACV,WAAO,KAAK,IAAI,KAAK,OAAO,CAAC;AAAA,EAC/B;AAAA,EACA,OAAO,OAAO;AACZ,UAAM,MAAM,KAAK,MAAM;AACvB,QAAI,IAAI;AACR,WAAO,KAAK,GAAG,GAAG;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe;AACb,aAAS,YAAY,KAAK;AACxB,YAAM,MAAM,MAAM;AAClB,aAAO,OAAO,UAAU,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS,OAAO,GAAG;AAAA,IAC3E;AACA,UAAM,IAAI,YAAY,KAAK,CAAC;AAC5B,UAAM,IAAI,YAAY,KAAK,CAAC;AAC5B,UAAM,IAAI,YAAY,KAAK,CAAC;AAC5B,WAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,EAC5C;AAAA,EACA,SAAS;AACP,QAAI,OAAO,KAAK,OAAO,aAAa;AAClC,YAAM,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO;AAC1C,UAAI,UAAU,GAAG;AACf,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,aAAK,KAAK,MAAM,MAAM,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM,KAAK,OAAO,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,QAAQ,EAAE;AAAA,MACpM;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,gBAAgB;AACd,QAAI,OAAO,KAAK,OAAO,aAAa;AAClC,YAAM,QAAQ,KAAK,OAAO,IAAI,KAAK,OAAO;AAC1C,UAAI,UAAU,GAAG;AACf,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,aAAK,KAAK,QAAQ,KAAK,OAAO;AAAA,MAChC;AAAA,IACF;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe;AACb,QAAI,OAAO,KAAK,OAAO,aAAa;AAClC,WAAK,MAAM,KAAK,OAAO,IAAI,KAAK,OAAO,KAAK;AAAA,IAC9C;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACT,QAAI,OAAO,KAAK,OAAO,aAAa;AAClC,WAAK,KAAK,KAAK,OAAO,IAAI;AAAA,IAC5B;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAgB;AACd,QAAI,OAAO,KAAK,gBAAgB,aAAa;AAC3C,WAAK,eAAe,KAAK,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,OAAO;AAAA,IACpE;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA,EAIA,OAAO,SAAS,IAAI;AAClB,UAAM,IAAI,KAAK,OAAO;AACtB,UAAM,IAAI,KAAK,cAAc;AAC7B,QAAI,IAAI,KAAK,aAAa,IAAI,SAAS;AACvC,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AACA,WAAO,KAAK,GAAG;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,SAAS,IAAI;AACnB,UAAM,IAAI,KAAK,OAAO;AACtB,UAAM,IAAI,KAAK,cAAc;AAC7B,QAAI,IAAI,KAAK,aAAa,IAAI,SAAS;AACvC,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AACA,WAAO,KAAK,GAAG;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO,SAAS,IAAI;AACtB,UAAM,QAAQ,KAAK,GAAG,KAAK;AAC3B,UAAM,IAAI,SAAS;AACnB,UAAM,OAAO,UAAQ,MAAM,GAAG,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG;AAC3D,UAAM,OAAO;AAAA,MACX,GAAG,MAAM,KAAK,GAAG,CAAC;AAAA,MAClB,GAAG,MAAM,KAAK,GAAG,CAAC;AAAA,MAClB,GAAG,MAAM,KAAK,GAAG,CAAC;AAAA,MAClB,GAAG,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI;AAAA,IAC9B;AACA,WAAO,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAS,IAAI;AAChB,WAAO,KAAK,IAAI;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG,MAAM;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS,IAAI;AACjB,WAAO,KAAK,IAAI;AAAA,MACd,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL,GAAG,MAAM;AAAA,EACX;AAAA,EACA,aAAa,YAAY;AACvB,UAAM,KAAK,KAAK,GAAG,UAAU;AAC7B,UAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK;AACxC,UAAM,OAAO,SAAO;AAClB,aAAO,OAAO,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,MAAM,KAAK;AAAA,IAC3E;AACA,WAAO,KAAK,GAAG;AAAA,MACb,GAAG,KAAK,GAAG;AAAA,MACX,GAAG,KAAK,GAAG;AAAA,MACX,GAAG,KAAK,GAAG;AAAA,MACX,GAAG;AAAA,IACL,CAAC;AAAA,EACH;AAAA;AAAA,EAGA,SAAS;AACP,WAAO,KAAK,cAAc,IAAI;AAAA,EAChC;AAAA,EACA,UAAU;AACR,WAAO,KAAK,cAAc,KAAK;AAAA,EACjC;AAAA;AAAA,EAGA,OAAO,OAAO;AACZ,WAAO,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM;AAAA,EAC5F;AAAA,EACA,QAAQ;AACN,WAAO,KAAK,GAAG,IAAI;AAAA,EACrB;AAAA;AAAA,EAGA,cAAc;AACZ,QAAI,MAAM;AACV,UAAM,QAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AACtC,WAAO,KAAK,WAAW,IAAI,OAAO,MAAM;AACxC,UAAM,QAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AACtC,WAAO,KAAK,WAAW,IAAI,OAAO,MAAM;AACxC,UAAM,QAAQ,KAAK,KAAK,GAAG,SAAS,EAAE;AACtC,WAAO,KAAK,WAAW,IAAI,OAAO,MAAM;AACxC,QAAI,OAAO,KAAK,MAAM,YAAY,KAAK,KAAK,KAAK,KAAK,IAAI,GAAG;AAC3D,YAAM,OAAO,MAAM,KAAK,IAAI,GAAG,EAAE,SAAS,EAAE;AAC5C,aAAO,KAAK,WAAW,IAAI,OAAO,MAAM;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO;AAAA,MACL,GAAG,KAAK,OAAO;AAAA,MACf,GAAG,KAAK,cAAc;AAAA,MACtB,GAAG,KAAK,aAAa;AAAA,MACrB,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA;AAAA,EAGA,cAAc;AACZ,UAAM,IAAI,KAAK,OAAO;AACtB,UAAM,IAAI,MAAM,KAAK,cAAc,IAAI,GAAG;AAC1C,UAAM,IAAI,MAAM,KAAK,aAAa,IAAI,GAAG;AACzC,WAAO,KAAK,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;AAAA,EAChF;AAAA;AAAA,EAGA,QAAQ;AACN,WAAO;AAAA,MACL,GAAG,KAAK,OAAO;AAAA,MACf,GAAG,KAAK,cAAc;AAAA,MACtB,GAAG,KAAK,SAAS;AAAA,MACjB,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,QAAQ;AACN,WAAO;AAAA,MACL,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,MACR,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,MAAM,IAAI,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC;AAAA,EAC3G;AAAA,EACA,WAAW;AACT,WAAO,KAAK,YAAY;AAAA,EAC1B;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK,OAAO,KAAK;AACnB,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,GAAG,IAAI,WAAW,OAAO,GAAG;AAClC,WAAO;AAAA,EACT;AAAA,EACA,GAAG,OAAO;AACR,WAAO,IAAI,KAAK,YAAY,KAAK;AAAA,EACnC;AAAA,EACA,SAAS;AACP,QAAI,OAAO,KAAK,SAAS,aAAa;AACpC,WAAK,OAAO,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,IAC7C;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,SAAS;AACP,QAAI,OAAO,KAAK,SAAS,aAAa;AACpC,WAAK,OAAO,KAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;AAAA,IAC7C;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,gBAAgB,QAAQ,QAAQ,KAAK,EAAE;AAC7C,aAAS,WAAW,QAAQ,QAAQ;AAClC,aAAO,SAAS,cAAc,MAAM,IAAI,cAAc,UAAU,MAAM,GAAG,EAAE;AAAA,IAC7E;AACA,QAAI,cAAc,SAAS,GAAG;AAE5B,WAAK,IAAI,WAAW,CAAC;AACrB,WAAK,IAAI,WAAW,CAAC;AACrB,WAAK,IAAI,WAAW,CAAC;AACrB,WAAK,IAAI,cAAc,CAAC,IAAI,WAAW,CAAC,IAAI,MAAM;AAAA,IACpD,OAAO;AAEL,WAAK,IAAI,WAAW,GAAG,CAAC;AACxB,WAAK,IAAI,WAAW,GAAG,CAAC;AACxB,WAAK,IAAI,WAAW,GAAG,CAAC;AACxB,WAAK,IAAI,cAAc,CAAC,IAAI,WAAW,GAAG,CAAC,IAAI,MAAM;AAAA,IACvD;AAAA,EACF;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,KAAK,IAAI;AACd,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,QAAI,KAAK,GAAG;AACV,YAAM,MAAM,MAAM,IAAI,GAAG;AACzB,WAAK,IAAI;AACT,WAAK,IAAI;AACT,WAAK,IAAI;AAAA,IACX;AACA,QAAI,IAAI,GACN,IAAI,GACJ,IAAI;AACN,UAAM,WAAW,IAAI;AACrB,UAAM,UAAU,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,KAAK;AAC3C,UAAM,kBAAkB,UAAU,IAAI,KAAK,IAAI,WAAW,IAAI,CAAC;AAC/D,QAAI,YAAY,KAAK,WAAW,GAAG;AACjC,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,YAAY,KAAK,WAAW,GAAG;AACxC,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,YAAY,KAAK,WAAW,GAAG;AACxC,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,YAAY,KAAK,WAAW,GAAG;AACxC,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,YAAY,KAAK,WAAW,GAAG;AACxC,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,YAAY,KAAK,WAAW,GAAG;AACxC,UAAI;AACJ,UAAI;AAAA,IACN;AACA,UAAM,wBAAwB,IAAI,SAAS;AAC3C,SAAK,IAAI,OAAO,IAAI,yBAAyB,GAAG;AAChD,SAAK,IAAI,OAAO,IAAI,yBAAyB,GAAG;AAChD,SAAK,IAAI,OAAO,IAAI,yBAAyB,GAAG;AAAA,EAClD;AAAA,EACA,QAAQ;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,SAAK,KAAK,IAAI;AACd,SAAK,KAAK;AACV,SAAK,KAAK;AACV,SAAK,IAAI,OAAO,MAAM,WAAW,IAAI;AACrC,UAAM,KAAK,MAAM,IAAI,GAAG;AACxB,SAAK,IAAI;AACT,SAAK,IAAI;AACT,SAAK,IAAI;AACT,QAAI,KAAK,GAAG;AACV;AAAA,IACF;AACA,UAAM,KAAK,IAAI;AACf,UAAM,IAAI,KAAK,MAAM,EAAE;AACvB,UAAM,KAAK,KAAK;AAChB,UAAM,IAAI,MAAM,KAAK,IAAM,KAAK,GAAG;AACnC,UAAM,IAAI,MAAM,KAAK,IAAM,IAAI,MAAM,GAAG;AACxC,UAAM,IAAI,MAAM,KAAK,IAAM,KAAK,IAAM,OAAO,GAAG;AAChD,YAAQ,GAAG;AAAA,MACT,KAAK;AACH,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,MACF,KAAK;AACH,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,MACF,KAAK;AACH,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,MACF,KAAK;AACH,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,MACF,KAAK;AACH,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,MACF,KAAK;AAAA,MACL;AACE,aAAK,IAAI;AACT,aAAK,IAAI;AACT;AAAA,IACJ;AAAA,EACF;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,QAAQ,cAAc,SAAS,aAAa;AAClD,SAAK,QAAQ;AAAA,MACX,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,QAAQ,cAAc,SAAS,aAAa;AAClD,SAAK,QAAQ;AAAA,MACX,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ,CAAC;AAAA,EACH;AAAA,EACA,cAAc,SAAS;AACrB,UAAM,QAAQ,cAAc,SAAS,CAAC,KAAK;AAAA;AAAA,MAE3C,IAAI,SAAS,GAAG,IAAI,MAAM,MAAM,MAAM,GAAG,IAAI;AAAA,KAAG;AAChD,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,MAAM,CAAC;AAChB,SAAK,IAAI,MAAM,CAAC;AAAA,EAClB;AACF;;;ACnhBA,IAAI,UAAU;AACd,IAAI,iBAAiB;AACrB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAGrB,IAAI,eAAe,CAAC;AAAA,EAClB,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,GAAG;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,SAAS,OAAO,KAAK,GAAG,OAAO;AAC7B,MAAI;AAEJ,MAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC,KAAK,KAAK;AACvD,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF,OAAO;AACL,UAAM,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU,IAAI,KAAK,MAAM,IAAI,CAAC,IAAI,UAAU;AAAA,EAChF;AACA,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,KAAK;AACrB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,cAAc,KAAK,GAAG,OAAO;AAEpC,MAAI,IAAI,MAAM,KAAK,IAAI,MAAM,GAAG;AAC9B,WAAO,IAAI;AAAA,EACb;AACA,MAAI;AACJ,MAAI,OAAO;AACT,iBAAa,IAAI,IAAI,iBAAiB;AAAA,EACxC,WAAW,MAAM,gBAAgB;AAC/B,iBAAa,IAAI,IAAI;AAAA,EACvB,OAAO;AACL,iBAAa,IAAI,IAAI,kBAAkB;AAAA,EACzC;AAEA,MAAI,aAAa,GAAG;AAClB,iBAAa;AAAA,EACf;AAEA,MAAI,SAAS,MAAM,mBAAmB,aAAa,KAAK;AACtD,iBAAa;AAAA,EACf;AACA,MAAI,aAAa,MAAM;AACrB,iBAAa;AAAA,EACf;AACA,SAAO,KAAK,MAAM,aAAa,GAAG,IAAI;AACxC;AACA,SAAS,SAAS,KAAK,GAAG,OAAO;AAC/B,MAAI;AACJ,MAAI,OAAO;AACT,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC,OAAO;AACL,YAAQ,IAAI,IAAI,kBAAkB;AAAA,EACpC;AAEA,UAAQ,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC;AACtC,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AACe,SAAR,SAA0B,OAAO;AACtC,MAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAChF,MAAI,WAAW,CAAC;AAChB,MAAI,SAAS,IAAI,UAAU,KAAK;AAChC,MAAI,MAAM,OAAO,MAAM;AACvB,WAAS,IAAI,iBAAiB,IAAI,GAAG,KAAK,GAAG;AAC3C,QAAI,IAAI,IAAI,UAAU;AAAA,MACpB,GAAG,OAAO,KAAK,GAAG,IAAI;AAAA,MACtB,GAAG,cAAc,KAAK,GAAG,IAAI;AAAA,MAC7B,GAAG,SAAS,KAAK,GAAG,IAAI;AAAA,IAC1B,CAAC;AACD,aAAS,KAAK,CAAC;AAAA,EACjB;AACA,WAAS,KAAK,MAAM;AACpB,WAAS,KAAK,GAAG,MAAM,gBAAgB,MAAM,GAAG;AAC9C,QAAI,KAAK,IAAI,UAAU;AAAA,MACrB,GAAG,OAAO,KAAK,EAAE;AAAA,MACjB,GAAG,cAAc,KAAK,EAAE;AAAA,MACxB,GAAG,SAAS,KAAK,EAAE;AAAA,IACrB,CAAC;AACD,aAAS,KAAK,EAAE;AAAA,EAClB;AAGA,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,aAAa,IAAI,SAAU,MAAM;AACtC,UAAI,QAAQ,KAAK,OACf,SAAS,KAAK;AAChB,aAAO,IAAI,UAAU,KAAK,mBAAmB,SAAS,EAAE,IAAI,SAAS,KAAK,GAAG,MAAM,EAAE,YAAY;AAAA,IACnG,CAAC;AAAA,EACH;AACA,SAAO,SAAS,IAAI,SAAUA,IAAG;AAC/B,WAAOA,GAAE,YAAY;AAAA,EACvB,CAAC;AACH;;;AC7HO,IAAI,sBAAsB;AAAA,EAC/B,OAAO;AAAA,EACP,WAAW;AAAA,EACX,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,WAAW;AAAA,EACX,QAAQ;AACV;AACO,IAAI,MAAM,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC9H,IAAI,UAAU,IAAI,CAAC;AACZ,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,QAAQ,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAChI,MAAM,UAAU,MAAM,CAAC;AAChB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,SAAS,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACjI,OAAO,UAAU,OAAO,CAAC;AAClB,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,OAAO,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAC/H,KAAK,UAAU,KAAK,CAAC;AACd,IAAI,OAAO;AACX,IAAI,iBAAiB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACO,IAAI,UAAU,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AAClI,QAAQ,UAAU,QAAQ,CAAC;AACpB,IAAI,cAAc,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACtI,YAAY,UAAU,YAAY,CAAC;AAC5B,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,YAAY,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACpI,UAAU,UAAU,UAAU,CAAC;AACxB,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,eAAe,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACvI,aAAa,UAAU,aAAa,CAAC;AAC9B,IAAI,aAAa,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACrI,WAAW,UAAU,WAAW,CAAC;AAC1B,IAAI,cAAc,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACtI,YAAY,UAAU,YAAY,CAAC;AAC5B,IAAI,WAAW,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;AACnI,SAAS,UAAU,SAAS,CAAC;AACtB,IAAI,qBAAqB;AAAA,EAC9B,KAAK;AAAA,EACL,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,MAAM;AACR;", "names": ["c"]}