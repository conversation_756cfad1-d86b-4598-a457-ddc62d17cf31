@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules\jest-cli\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules\jest-cli\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules\jest-cli\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules\jest-cli\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\jest-cli@29.7.0_@types+node@20.19.9_ts-node@10.9.2\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\jest-cli\bin\jest.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\jest-cli\bin\jest.js" %*
)
