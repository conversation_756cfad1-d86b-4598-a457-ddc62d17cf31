<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作流画布布局测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            height: 100vh;
            overflow: hidden;
        }

        .workflow-define-canvas {
            display: flex;
            flex-direction: column;
            width: 100%;
            height: calc(100vh - 85px - 40px);
            background-color: #ffffff;
            position: relative;
        }

        .toolbar {
            height: 60px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #dfe3e8;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-weight: bold;
        }

        .canvas-container {
            display: flex;
            flex: 1;
            height: 100%;
            border: 1px solid #dfe3e8;
            border-top: none;
            overflow: hidden;
            position: relative;
        }

        .workflow-stencil {
            width: 400px;
            height: 100%;
            border-right: 1px solid #dfe3e8;
            position: relative;
            background-color: #fff;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .workflow-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            background-color: #fff;
            overflow: hidden;
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
            font-size: 16px;
            font-weight: bold;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        .workflow-sidebar {
            width: 400px;
            height: 100%;
            background-color: #fff;
            border-left: 1px solid #dfe3e8;
            position: relative;
            transition: width 0.3s ease;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        .sidebar-collapsed {
            width: 40px !important;
        }

        .toggle-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 5px 10px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .toggle-btn:hover {
            background: #40a9ff;
        }

        .size-info {
            position: absolute;
            bottom: 10px;
            left: 10px;
            font-size: 12px;
            color: #999;
            background: rgba(255, 255, 255, 0.8);
            padding: 4px 8px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="workflow-define-canvas">
        <!-- 工具栏 -->
        <div class="toolbar">
            工作流设计器 - 布局测试
        </div>

        <!-- 主要内容区域 -->
        <div class="canvas-container">
            <!-- 左侧组件面板 -->
            <div class="workflow-stencil" id="stencil">
                <div>
                    <div>左侧组件面板</div>
                    <div class="size-info" id="stencil-size">宽度: 400px</div>
                </div>
            </div>

            <!-- 中间画布区域 -->
            <div class="workflow-canvas" id="canvas">
                <div>
                    <div>中间画布区域 (自适应)</div>
                    <div class="size-info" id="canvas-size">宽度: 计算中...</div>
                </div>
            </div>

            <!-- 右侧属性面板 -->
            <div class="workflow-sidebar" id="sidebar">
                <div>
                    <div>右侧属性面板</div>
                    <button class="toggle-btn" onclick="toggleSidebar()">折叠</button>
                    <div class="size-info" id="sidebar-size">宽度: 400px</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let sidebarCollapsed = false;

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const btn = document.querySelector('.toggle-btn');
            
            sidebarCollapsed = !sidebarCollapsed;
            
            if (sidebarCollapsed) {
                sidebar.classList.add('sidebar-collapsed');
                btn.textContent = '展开';
            } else {
                sidebar.classList.remove('sidebar-collapsed');
                btn.textContent = '折叠';
            }
            
            updateSizeInfo();
        }

        function updateSizeInfo() {
            const stencil = document.getElementById('stencil');
            const canvas = document.getElementById('canvas');
            const sidebar = document.getElementById('sidebar');
            
            document.getElementById('stencil-size').textContent = `宽度: ${stencil.offsetWidth}px`;
            document.getElementById('canvas-size').textContent = `宽度: ${canvas.offsetWidth}px`;
            document.getElementById('sidebar-size').textContent = `宽度: ${sidebar.offsetWidth}px`;
        }

        // 监听窗口大小变化
        window.addEventListener('resize', updateSizeInfo);
        
        // 初始化
        setTimeout(updateSizeInfo, 100);
    </script>
</body>
</html>
