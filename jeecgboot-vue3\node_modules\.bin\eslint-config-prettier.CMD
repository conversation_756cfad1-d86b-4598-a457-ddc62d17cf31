@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules\eslint-config-prettier\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules\eslint-config-prettier\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules\eslint-config-prettier\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules\eslint-config-prettier\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\eslint-config-prettier@9.1.2_eslint@8.57.1\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
)
