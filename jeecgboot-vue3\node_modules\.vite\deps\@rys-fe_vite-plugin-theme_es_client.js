import "./chunk-PLDDJCW6.js";

// node_modules/.pnpm/@rys-fe+vite-plugin-theme@0.8.6_vite@5.4.19/node_modules/@rys-fe/vite-plugin-theme/es/client.js
var globalField = "__VITE_THEME__";
var styleTagId = "__VITE_PLUGIN_THEME__";
var darkStyleTagId = "__VITE_PLUGIN_DARK_THEME__";
var linkID = "__VITE_PLUGIN_THEME-ANTD_DARK_THEME_LINK__";
var colorPluginOutputFileName = __COLOR_PLUGIN_OUTPUT_FILE_NAME__;
var isProd = __PROD__;
var colorPluginOptions = __COLOR_PLUGIN_OPTIONS__;
var injectTo = colorPluginOptions.injectTo;
var debounceThemeRender = debounce(200, renderTheme);
var darkCssIsReady = false;
(() => {
  if (!window[globalField]) {
    window[globalField] = {
      styleIdMap: /* @__PURE__ */ new Map(),
      styleRenderQueueMap: /* @__PURE__ */ new Map()
    };
  }
  setGlobalOptions("replaceStyleVariables", replaceStyleVariables);
  if (!getGlobalOptions("defaultOptions")) {
    setGlobalOptions("defaultOptions", colorPluginOptions);
  }
})();
function addCssToQueue(id, styleString) {
  const styleIdMap = getGlobalOptions("styleIdMap");
  if (!styleIdMap.get(id)) {
    window[globalField].styleRenderQueueMap.set(id, styleString);
    debounceThemeRender();
  }
}
function renderTheme() {
  const variables = getGlobalOptions("colorVariables");
  if (!variables) {
    return;
  }
  const styleRenderQueueMap = getGlobalOptions("styleRenderQueueMap");
  const styleDom = getStyleDom(styleTagId);
  let html = styleDom.innerHTML;
  for (const [id, css] of styleRenderQueueMap.entries()) {
    html += css;
    window[globalField].styleRenderQueueMap.delete(id);
    window[globalField].styleIdMap.set(id, css);
  }
  replaceCssColors(html, variables).then((processCss) => {
    appendCssToDom(styleDom, processCss, injectTo);
  });
}
async function replaceStyleVariables({ colorVariables, customCssHandler }) {
  setGlobalOptions("colorVariables", colorVariables);
  const styleIdMap = getGlobalOptions("styleIdMap");
  const styleRenderQueueMap = getGlobalOptions("styleRenderQueueMap");
  if (!isProd) {
    for (const [id, css] of styleIdMap.entries()) {
      styleRenderQueueMap.set(id, css);
    }
    renderTheme();
  } else {
    try {
      const cssText = await fetchCss(colorPluginOutputFileName);
      const styleDom = getStyleDom(styleTagId);
      const processCss = await replaceCssColors(cssText, colorVariables, customCssHandler);
      appendCssToDom(styleDom, processCss, injectTo);
    } catch (error) {
      throw new Error(error);
    }
  }
}
async function loadDarkThemeCss() {
  const extractCss = __ANTD_DARK_PLUGIN_EXTRACT_CSS__;
  const isLoadLink = __ANTD_DARK_PLUGIN_LOAD_LINK__;
  if (darkCssIsReady || !extractCss) {
    return;
  }
  if (isLoadLink) {
    const linkTag = document.getElementById(linkID);
    if (linkTag) {
      linkTag.removeAttribute("disabled");
      linkTag.setAttribute("rel", "stylesheet");
    }
  } else {
    const colorPluginOutputFileName2 = __ANTD_DARK_PLUGIN_OUTPUT_FILE_NAME__;
    const cssText = await fetchCss(colorPluginOutputFileName2);
    const styleDom = getStyleDom(darkStyleTagId);
    appendCssToDom(styleDom, cssText, injectTo);
  }
  darkCssIsReady = true;
}
async function replaceCssColors(css, colors, customCssHandler) {
  let retCss = css;
  const defaultOptions = getGlobalOptions("defaultOptions");
  const colorVariables = defaultOptions ? defaultOptions.colorVariables || [] : [];
  colorVariables.forEach(function(color, index) {
    const reg = new RegExp(color.replace(/,/g, ",\\s*").replace(/\s/g, "").replace("(", `\\(`).replace(")", `\\)`) + "([\\da-f]{2})?(\\b|\\)|,|\\s)?", "ig");
    retCss = retCss.replace(reg, colors[index] + "$1$2").replace("$1$2", "");
    if (customCssHandler && typeof customCssHandler === "function") {
      retCss = customCssHandler(retCss) || retCss;
    }
  });
  return retCss;
}
function setGlobalOptions(key, value) {
  window[globalField][key] = value;
}
function getGlobalOptions(key) {
  return window[globalField][key];
}
function getStyleDom(id) {
  let style = document.getElementById(id);
  if (!style) {
    style = document.createElement("style");
    style.setAttribute("id", id);
  }
  return style;
}
async function appendCssToDom(styleDom, cssText, appendTo = "body") {
  styleDom.innerHTML = cssText;
  if (appendTo === "head") {
    document.head.appendChild(styleDom);
  } else if (appendTo === "body") {
    document.body.appendChild(styleDom);
  } else if (appendTo === "body-prepend") {
    const firstChildren = document.body.firstChild;
    document.body.insertBefore(styleDom, firstChildren);
  }
}
function fetchCss(fileName) {
  return new Promise((resolve, reject) => {
    const append = getGlobalOptions("appended");
    if (append) {
      setGlobalOptions("appended", false);
      resolve("");
      return;
    }
    const xhr = new XMLHttpRequest();
    xhr.onload = function() {
      if (xhr.readyState === 4) {
        if (xhr.status === 200) {
          resolve(xhr.responseText);
        } else {
          reject(xhr.status);
        }
      }
    };
    xhr.onerror = function(e) {
      reject(e);
    };
    xhr.ontimeout = function(e) {
      reject(e);
    };
    xhr.open("GET", fileName, true);
    xhr.send();
  });
}
function debounce(delay, fn) {
  let timer;
  return function(...args) {
    const ctx = this;
    clearTimeout(timer);
    timer = setTimeout(function() {
      fn.apply(ctx, args);
    }, delay);
  };
}
export {
  addCssToQueue,
  appendCssToDom,
  darkCssIsReady,
  darkStyleTagId,
  getGlobalOptions,
  getStyleDom,
  globalField,
  linkID,
  loadDarkThemeCss,
  replaceCssColors,
  replaceStyleVariables,
  setGlobalOptions,
  styleTagId
};
//# sourceMappingURL=@rys-fe_vite-plugin-theme_es_client.js.map
