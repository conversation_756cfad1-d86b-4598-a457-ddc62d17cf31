import { ref, onUnmounted, Ref } from 'vue';
import { Graph, Shape } from '@antv/x6';
import { Transform } from '@antv/x6-plugin-transform';
import { Selection } from '@antv/x6-plugin-selection';
import { Snapline } from '@antv/x6-plugin-snapline';
import type { WorkflowGraphInstance, SwimLaneData } from '../types/workflow';

/**
 * 工作流图形实例管理 Hook
 * 1、读取data渲染流程图
 * 2、流程图转JSON
 */
export function useWorkflowGraph(): WorkflowGraphInstance {
  const graph = ref<Graph | null>(null) as Ref<Graph | null>;

  /**
   * 初始化图形实例
   */
  const initGraph = (container: HTMLElement) => {
    if (!container) {
      return;
    }

    console.log(container.clientWidth);

    // 创建图表实例
    graph.value = new Graph({
      container,
      width: container.clientWidth,
      height: container.clientHeight,
      grid: {
        size: 10,
        visible: true,
        type: 'dot',
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3,
      },
      // 启用交互功能
      interacting: {
        nodeMovable: true,
        edgeMovable: true,
        edgeLabelMovable: true,
        arrowheadMovable: true,
        vertexMovable: true,
        vertexAddable: true,
        vertexDeletable: true,
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: { radius: 8 },
        },
        anchor: 'center',
        connectionPoint: 'anchor',
        allowBlank: false,
        snap: { radius: 20 },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A2B1C3',
                strokeWidth: 2,
                targetMarker: { name: 'block', width: 12, height: 8 },
              },
            },
            zIndex: 0,
          });
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet;
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: { attrs: { fill: '#5F95FF', stroke: '#5F95FF' } },
        },
      },
      translating: {
        restrict(cellView: any) {
          // 检查 cellView 是否存在
          if (!cellView || !cellView.cell) {
            return null;
          }
          const cell = cellView.cell;

          // 检查 cell 是否有必要的方法
          if (!cell || typeof cell.prop !== 'function' || typeof cell.getBBox !== 'function') {
            return null;
          }

          try {
            const parentId = cell.prop('parent');
            if (parentId) {
              const parentNode = graph.value?.getCellById(parentId);
              if (parentNode && typeof parentNode.getBBox === 'function') {
                return parentNode.getBBox().moveAndExpand({
                  x: 0,
                  y: 30,
                  width: 0,
                  height: -30,
                });
              }
            }
            return cell.getBBox();
          } catch (error) {
            // 如果出现任何错误，返回 null 表示不限制移动
            return null;
          }
        },
      },
    });

    // 初始化插件
    if (graph.value) {
      graph.value
        .use(new Transform({ resizing: true, rotating: true }))
        .use(new Selection({ rubberband: true, showNodeSelectionBox: true }))
        .use(new Snapline());

      // 监听节点添加事件，确保新添加的节点具有正确的交互属性
      graph.value.on('node:added', ({ node }) => {
        // 检查节点是否存在且有必要的方法
        if (!node || typeof node.prop !== 'function' || typeof node.getAttrs !== 'function') {
          return;
        }

        try {
          // 确保节点可以移动
          if (node.prop('movable') !== false) {
            node.prop('movable', true);
          }

          // 确保节点具有正确的样式
          const currentAttrs = node.getAttrs();
          if (currentAttrs && currentAttrs.body && !currentAttrs.body.cursor) {
            node.setAttrs({
              body: {
                ...currentAttrs.body,
                cursor: 'move',
              },
            });
          }
          if (currentAttrs && currentAttrs.text && !currentAttrs.text.cursor) {
            node.setAttrs({
              text: {
                ...currentAttrs.text,
                cursor: 'move',
              },
            });
          }
        } catch (error) {
          // 如果设置属性时出错，静默处理
        }
      });
    }
  };

  /**
   * 销毁图形实例
   */
  const destroyGraph = () => {
    if (graph.value) {
      graph.value.dispose();
      graph.value = null;
    }
  };

  /**
   * 获取图形数据
   */
  const getGraphData = () => {
    if (!graph.value) return null;
    return graph.value.toJSON();
  };

  /**
   * 加载图形数据
   */
  const loadGraphData = (data: SwimLaneData) => {
    if (!graph.value) {
      return;
    }
    // 清空现有内容
    graph.value.clearCells();

    const cells: any[] = [];

    // 检查数据结构
    if (!data.lanes) data.lanes = [];
    if (!data.nodes) data.nodes = [];
    if (!data.edges) data.edges = [];

    // 1. 先创建泳道节点
    data.lanes.forEach((lane) => {
      const laneNode = graph.value!.createNode(lane);
      cells.push(laneNode);
    });

    // 2. 处理节点
    data.nodes.forEach((node) => {
      const nodeItem = graph.value!.createNode(node);
      cells.push(nodeItem);
    });

    // 3. 处理边
    data.edges.forEach((edge) => {
      const edgeItem = graph.value!.createEdge(edge);
      cells.push(edgeItem);
    });

    graph.value.resetCells(cells);
    graph.value.zoomToFit({ padding: 10, maxScale: 1 });
  };

  // 组件卸载时清理资源
  onUnmounted(() => {
    destroyGraph();
  });

  return {
    graph,
    initGraph,
    destroyGraph,
    getGraphData,
    loadGraphData,
  };
}
