@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\dist\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\dist\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\dist\bin\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\dist\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules\sass-embedded\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\sass-embedded@1.89.2\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\sass-embedded\dist\bin\sass.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\sass-embedded\dist\bin\sass.js" %*
)
