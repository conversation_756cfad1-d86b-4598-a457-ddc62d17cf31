<template>
  <div class="workflow-canvas" ref="canvasContainerRef">
    <!-- X6 Graph 画布将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
  import { Graph } from '@antv/x6';
  import { useWorkflowGraph } from './hooks/useWorkflowGraph';
  import { useWorkflowEvents } from './hooks/useWorkflowEvents';
  import type { SwimLaneData, WorkflowEventHandlers } from './types/workflow';

  interface Props {
    width?: string | number;
    height?: string | number;
    data?: SwimLaneData | null;
    readonly?: boolean;
  }

  interface Emits {
    (e: 'ready', graph: Graph): void;
    (e: 'dataChange', data: any): void;
    (e: 'selectionChange', selection: { nodes: any[]; edges: any[] }): void;
    (e: 'nodeClick', node: any): void;
    (e: 'edgeClick', edge: any): void;
    (e: 'blankClick'): void;
  }

  const emit = defineEmits<Emits>();

  const props = withDefaults(defineProps<Props>(), {
    width: '100%',
    height: '100%',
    readonly: false,
  });

  const canvasContainerRef = ref<HTMLElement | null>(null);
  const isReady = ref(false);

  // 使用图形管理 Hook
  const { graph, initGraph, destroyGraph, getGraphData, loadGraphData } = useWorkflowGraph();

  // 使用事件处理 Hook
  const {
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    bindGraphEvents,
    unbindGraphEvents,
    initKeyboardShortcuts,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  } = useWorkflowEvents(graph);

  /**
   * 初始化画布
   */
  const initCanvas = async () => {
    if (!canvasContainerRef.value) {
      return;
    }

    try {
      // 等待 DOM 更新
      await nextTick();

      // 设置容器样式
      const container = canvasContainerRef.value;
      container.style.width = typeof props.width === 'number' ? `${props.width}px` : props.width;
      container.style.height = typeof props.height === 'number' ? `${props.height}px` : props.height;

      // 初始化图形实例
      initGraph(container);

      if (graph.value) {
        // 自定义事件处理器
        const customEventHandlers: Partial<WorkflowEventHandlers> = {
          onNodeClick: ({ node }) => {
            emit('nodeClick', {
              id: node.id,
              shape: node.shape,
              position: node.getPosition(),
              size: node.getSize(),
              data: node.getData(),
            });
          },
          onEdgeClick: ({ edge }) => {
            emit('edgeClick', {
              id: edge.id,
              source: edge.getSource(),
              target: edge.getTarget(),
              data: edge.getData(),
            });
          },
          onBlankClick: () => {
            emit('blankClick');
          },
        };

        // 绑定事件
        bindGraphEvents(container, customEventHandlers);

        // 初始化快捷键（如果不是只读模式）
        if (!props.readonly) {
          initKeyboardShortcuts({
            onUndo: () => {
              // 撤销功能需要history插件支持
            },
            onRedo: () => {
              // 重做功能需要history插件支持
            },
            onDelete: () => {
              const cells = graph.value?.getSelectedCells();
              if (cells?.length) {
                graph.value?.removeCells(cells);
              }
            },
            onCopy: () => {
              const cells = graph.value?.getSelectedCells();
              if (cells?.length) {
                // 复制功能需要clipboard插件支持
              }
            },
            onPaste: () => {
              // 粘贴功能需要clipboard插件支持
            },
          });
        }

        // 监听数据变化
        graph.value.on('cell:added', () => {
          emit('dataChange', getGraphData());
        });

        graph.value.on('cell:removed', () => {
          emit('dataChange', getGraphData());
        });

        graph.value.on('cell:changed', () => {
          emit('dataChange', getGraphData());
        });

        // 监听选择变化
        graph.value.on('selection:changed', ({ selected }) => {
          const nodes = selected
            .filter((cell) => cell.isNode())
            .map((node) => ({
              id: node.id,
              data: node.getData(),
            }));
          const edges = selected
            .filter((cell) => cell.isEdge())
            .map((edge) => ({
              id: edge.id,
              data: edge.getData(),
            }));

          emit('selectionChange', { nodes, edges });
        });

        isReady.value = true;
        emit('ready', graph.value);

        // 如果有初始数据，加载它
        if (props.data) {
          loadData(props.data);
        }
      }
    } catch (error) {
      console.error('初始化画布失败:', error);
    }
  };

  /**
   * 加载数据到画布
   */
  const loadData = (data: SwimLaneData) => {
    if (!graph.value) {
      return;
    }
    if (!data) {
      return;
    }

    try {
      loadGraphData(data);
    } catch (error) {
      throw error;
    }
  };

  /**
   * 获取画布数据
   */
  const getData = () => {
    return getGraphData();
  };

  /**
   * 清空画布
   */
  const clearCanvas = () => {
    if (!graph.value) return;
    graph.value.clearCells();
  };

  /**
   * 缩放到适合大小
   */
  const zoomToFit = (options?: { padding?: number; maxScale?: number }) => {
    if (!graph.value) return;
    graph.value.zoomToFit({ padding: 10, maxScale: 1, ...options });
  };

  /**
   * 设置缩放比例
   */
  const setZoom = (scale: number) => {
    if (!graph.value) return;
    graph.value.scale(scale);
  };

  /**
   * 获取当前缩放比例
   */
  const getZoom = () => {
    if (!graph.value) return 1;
    return graph.value.zoom();
  };

  /**
   * 居中显示
   */
  const centerContent = () => {
    if (!graph.value) return;
    graph.value.centerContent();
  };

  /**
   * 导出为图片
   */
  const exportImage = (_type: 'PNG' | 'JPEG' | 'SVG' = 'PNG') => {
    if (!graph.value) return null;

    return new Promise((resolve, reject) => {
      try {
        // 导出功能需要export插件支持
        resolve(null);
      } catch (error) {
        reject(error);
      }
    });
  };

  // 监听数据变化
  watch(
    () => props.data,
    (newData) => {
      if (newData && isReady.value) {
        loadData(newData);
        // 数据加载后触发数据变化事件
        nextTick(() => {
          if (graph.value) {
            const graphData = getGraphData();
            emit('dataChange', graphData);
          }
        });
      }
    },
    { deep: true }
  );

  // 监听只读模式变化
  watch(
    () => props.readonly,
    (_readonly) => {
      if (!graph.value) return;

      // 设置图形的交互性（需要interacting插件支持）
      // graph.value.setInteracting(!readonly);
    }
  );

  onMounted(() => {
    initCanvas();
  });

  onUnmounted(() => {
    unbindGraphEvents();
    destroyGraph();
  });

  // 暴露方法给父组件
  defineExpose({
    graph,
    isReady,
    selectedNode,
    selectedEdge,
    showPropertyPanel,
    loadData,
    getData,
    clearCanvas,
    zoomToFit,
    setZoom,
    getZoom,
    centerContent,
    exportImage,
    getSelectedNodeData,
    updateSelectedNodeData,
    getSelectedEdgeData,
    updateSelectedEdgeData,
  });
</script>

<style lang="less" scoped>
  .workflow-canvas {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #fff;
    overflow: hidden;

    // X6 样式覆盖
    :deep(.x6-widget-transform) {
      margin: -1px 0 0 -1px;
      padding: 0px;
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-transform > div) {
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-transform > div:hover) {
      background-color: #3dafe4;
    }

    :deep(.x6-widget-transform-active-handle) {
      background-color: #3dafe4;
    }

    :deep(.x6-widget-transform-resize) {
      border-radius: 0;
    }

    :deep(.x6-widget-selection-inner) {
      border: 1px solid #239edd;
    }

    :deep(.x6-widget-selection-box) {
      opacity: 0;
    }

    :deep(.x6-node:hover .topic-image) {
      visibility: visible;
    }

    :deep(.x6-node-selected rect) {
      stroke-width: 2px;
    }

    // 连接桩样式
    :deep(.x6-port-body) {
      transition: all 0.3s;
    }

    :deep(.x6-port-body:hover) {
      stroke-width: 2px;
      stroke: #1890ff;
    }
  }
</style>
