@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@4.9.5\node_modules\ts-jest\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@4.9.5\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@4.9.5\node_modules\ts-jest\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\ts-jest@29.4.0_@babel+core@7.28.0_jest@29.7.0_typescript@4.9.5\node_modules;D:\JavaProjectSpace\haerbin_bank_newframework_b\jeecgboot-vue3\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\ts-jest\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\ts-jest\cli.js" %*
)
