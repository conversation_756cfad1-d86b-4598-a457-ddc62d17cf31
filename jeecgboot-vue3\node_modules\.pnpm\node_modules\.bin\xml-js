#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules/xml-js/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules/xml-js/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules/xml-js/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules/xml-js/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/xml-js@1.6.11/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../xml-js/bin/cli.js" "$@"
else
  exec node  "$basedir/../xml-js/bin/cli.js" "$@"
fi
