{"version": 3, "sources": ["../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/utils/store.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/utils/data.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/utils/frequently.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/components/index.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/polyfills/stringFromCodePoint.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/utils/index.js", "../../.pnpm/emoji-mart-vue-fast@15.0.4_vue@3.5.17/node_modules/emoji-mart-vue-fast/src/utils/emoji-data.js"], "sourcesContent": ["var NAMESPACE = 'emoji-mart'\n\nconst _JSON = JSON\n\nvar isLocalStorageSupported =\n  typeof window !== 'undefined' && 'localStorage' in window\n\nlet getter\nlet setter\n\nfunction setHandlers(handlers) {\n  handlers || (handlers = {})\n\n  getter = handlers.getter\n  setter = handlers.setter\n}\n\nfunction setNamespace(namespace) {\n  NAMESPACE = namespace\n}\n\nfunction update(state) {\n  for (let key in state) {\n    let value = state[key]\n    set(key, value)\n  }\n}\n\nfunction set(key, value) {\n  if (setter) {\n    setter(key, value)\n  } else {\n    if (!isLocalStorageSupported) return\n    try {\n      window.localStorage[`${NAMESPACE}.${key}`] = _JSON.stringify(value)\n    } catch (e) {}\n  }\n}\n\nfunction get(key) {\n  if (getter) {\n    return getter(key)\n  } else {\n    if (!isLocalStorageSupported) return\n    try {\n      var value = window.localStorage[`${NAMESPACE}.${key}`]\n    } catch (e) {\n      return\n    }\n\n    if (value) {\n      return JSON.parse(value)\n    }\n  }\n}\n\nexport default { update, set, get, setNamespace, setHandlers }\n", "const mapping = {\n  name: 'a',\n  unified: 'b',\n  non_qualified: 'c',\n  has_img_apple: 'd',\n  has_img_google: 'e',\n  has_img_twitter: 'f',\n  has_img_facebook: 'h',\n  keywords: 'j',\n  sheet: 'k',\n  emoticons: 'l',\n  text: 'm',\n  short_names: 'n',\n  added_in: 'o',\n}\n\nconst buildSearch = (emoji) => {\n  const search = []\n\n  var addToSearch = (strings, split) => {\n    if (!strings) {\n      return\n    }\n\n    ;(Array.isArray(strings) ? strings : [strings]).forEach((string) => {\n      ;(split ? string.split(/[-|_|\\s]+/) : [string]).forEach((s) => {\n        s = s.toLowerCase()\n\n        if (search.indexOf(s) == -1) {\n          search.push(s)\n        }\n      })\n    })\n  }\n\n  addToSearch(emoji.short_names, true)\n  addToSearch(emoji.name, true)\n  addToSearch(emoji.keywords, false)\n  addToSearch(emoji.emoticons, false)\n\n  return search.join(',')\n}\n\nfunction deepFreeze(object) {\n  // Retrieve the property names defined on object\n  var propNames = Object.getOwnPropertyNames(object)\n\n  // Freeze properties before freezing self\n  for (let name of propNames) {\n    let value = object[name]\n    object[name] =\n      value && typeof value === 'object' ? deepFreeze(value) : value\n  }\n  return Object.freeze(object)\n}\n\nconst uncompress = (data) => {\n  if (!data.compressed) {\n    return data\n  }\n  data.compressed = false\n\n  for (let id in data.emojis) {\n    let emoji = data.emojis[id]\n\n    for (let key in mapping) {\n      emoji[key] = emoji[mapping[key]]\n      delete emoji[mapping[key]]\n    }\n\n    if (!emoji.short_names) emoji.short_names = []\n    emoji.short_names.unshift(id)\n\n    emoji.sheet_x = emoji.sheet[0]\n    emoji.sheet_y = emoji.sheet[1]\n    delete emoji.sheet\n\n    if (!emoji.text) emoji.text = ''\n\n    if (!emoji.added_in) emoji.added_in = 6\n    emoji.added_in = emoji.added_in.toFixed(1)\n\n    emoji.search = buildSearch(emoji)\n  }\n  data = deepFreeze(data)\n  return data\n}\n\nexport { buildSearch, uncompress }\n", "import store from './store'\n\nconst DEFAULTS = [\n  '+1',\n  'grinning',\n  'kissing_heart',\n  'heart_eyes',\n  'laughing',\n  'stuck_out_tongue_winking_eye',\n  'sweat_smile',\n  'joy',\n  'scream',\n  'disappointed',\n  'unamused',\n  'weary',\n  'sob',\n  'sunglasses',\n  'heart',\n  'hankey',\n]\n\nlet frequently, initialized\nlet defaults = {}\n\nfunction init() {\n  initialized = true\n  frequently = store.get('frequently')\n}\n\nfunction add(emoji) {\n  if (!initialized) init()\n  var { id } = emoji\n\n  frequently || (frequently = defaults)\n  frequently[id] || (frequently[id] = 0)\n  frequently[id] += 1\n\n  store.set('last', id)\n  store.set('frequently', frequently)\n}\n\nfunction get(maxNumber) {\n  if (!initialized) init()\n  if (!frequently) {\n    defaults = {}\n\n    const result = []\n\n    let defaultLength = Math.min(maxNumber, DEFAULTS.length)\n    for (let i = 0; i < defaultLength; i++) {\n      defaults[DEFAULTS[i]] = parseInt((defaultLength - i) / 4, 10) + 1\n      result.push(DEFAULTS[i])\n    }\n\n    return result\n  }\n\n  const quantity = maxNumber\n  const frequentlyKeys = []\n\n  for (let key in frequently) {\n    if (frequently.hasOwnProperty(key)) {\n      frequentlyKeys.push(key)\n    }\n  }\n\n  const sorted = frequentlyKeys\n    .sort((a, b) => frequently[a] - frequently[b])\n    .reverse()\n  const sliced = sorted.slice(0, quantity)\n\n  const last = store.get('last')\n\n  if (last && sliced.indexOf(last) == -1) {\n    sliced.pop()\n    sliced.push(last)\n  }\n\n  return sliced\n}\n\nexport default { add, get }\n", "export { default as Anchors } from './anchors.vue'\nexport { default as Category } from './category.vue'\nexport { default as Preview } from './preview.vue'\nexport { default as Search } from './search.vue'\nexport { default as Skins } from './skins.vue'\nexport { default as Emoji } from './Emoji.vue'\nexport { default as Picker } from './Picker.vue'\n", "const _String = String\n\nexport default _String.fromCodePoint ||\n  function stringFromCodePoint() {\n    var MAX_SIZE = 0x4000\n    var codeUnits = []\n    var highSurrogate\n    var lowSurrogate\n    var index = -1\n    var length = arguments.length\n    if (!length) {\n      return ''\n    }\n    var result = ''\n    while (++index < length) {\n      var codePoint = Number(arguments[index])\n      if (\n        !isFinite(codePoint) || // `NaN`, `+Infinity`, or `-Infinity`\n        codePoint < 0 || // not a valid Unicode code point\n        codePoint > 0x10ffff || // not a valid Unicode code point\n        Math.floor(codePoint) != codePoint // not an integer\n      ) {\n        throw RangeError('Invalid code point: ' + codePoint)\n      }\n      if (codePoint <= 0xffff) {\n        // BMP code point\n        codeUnits.push(codePoint)\n      } else {\n        // Astral code point; split in surrogate halves\n        // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        codePoint -= 0x10000\n        highSurrogate = (codePoint >> 10) + 0xd800\n        lowSurrogate = (codePoint % 0x400) + 0xdc00\n        codeUnits.push(highSurrogate, lowSurrogate)\n      }\n      if (index + 1 === length || codeUnits.length > MAX_SIZE) {\n        result += String.fromCharCode.apply(null, codeUnits)\n        codeUnits.length = 0\n      }\n    }\n    return result\n  }\n", "import stringFromCodePoint from '../polyfills/stringFromCodePoint'\n\nfunction unifiedToNative(unified) {\n  var unicodes = unified.split('-'),\n    codePoints = unicodes.map((u) => `0x${u}`)\n\n  return stringFromCodePoint.apply(null, codePoints)\n}\n\nfunction uniq(arr) {\n  return arr.reduce((acc, item) => {\n    if (acc.indexOf(item) === -1) {\n      acc.push(item)\n    }\n    return acc\n  }, [])\n}\n\nfunction intersect(a, b) {\n  const uniqA = uniq(a)\n  const uniqB = uniq(b)\n\n  return uniqA.filter((item) => uniqB.indexOf(item) >= 0)\n}\n\nfunction deepMerge(a, b) {\n  var o = {}\n\n  for (let key in a) {\n    let originalValue = a[key],\n      value = originalValue\n\n    if (Object.prototype.hasOwnProperty.call(b, key)) {\n      value = b[key]\n    }\n\n    if (typeof value === 'object') {\n      value = deepMerge(originalValue, value)\n    }\n\n    o[key] = value\n  }\n\n  return o\n}\n\n// https://github.com/sonicdoe/measure-scrollbar\nfunction measureScrollbar() {\n  if (typeof document == 'undefined') return 0\n  const div = document.createElement('div')\n\n  div.style.width = '100px'\n  div.style.height = '100px'\n  div.style.overflow = 'scroll'\n  div.style.position = 'absolute'\n  div.style.top = '-9999px'\n\n  document.body.appendChild(div)\n  const scrollbarWidth = div.offsetWidth - div.clientWidth\n  document.body.removeChild(div)\n\n  return scrollbarWidth\n}\n\nexport { uniq, intersect, deepMerge, unifiedToNative, measureScrollbar }\n", "import { intersect, unifiedToNative } from './index'\nimport { uncompress, buildSearch } from './data'\nimport frequently from './frequently'\n\nconst SHEET_COLUMNS = 61\nconst COLONS_REGEX = /^(?:\\:([^\\:]+)\\:)(?:\\:skin-tone-(\\d)\\:)?$/\n// Skin tones\nconst SKINS = ['1F3FA', '1F3FB', '1F3FC', '1F3FD', '1F3FE', '1F3FF']\n\n/**\n * Emoji data structure:\n * {\n *    \"compressed\": false,\n *    \"aliases\": {\n *      collision: \"boom\"\n *      cooking: \"fried_egg\"\n *      envelope: \"email\"\n *      face_with_finger_covering_closed_lips: \"shushing_face\"\n *      ...\n *    },\n *    \"categories\": [ {\n *      id: \"people\",\n *      name: \"Smileys & Emotion\",\n *      emojis: [ \"grinning\", \"grin\", \"joy\", ... ]\n *    }, {\n *      id: \"nature\",\n *      name: \"Animals & Nature\",\n *      emojis: [ \"monkey_face\", \"money\", \"gorilla\", ... ]\n *    },\n *    ...\n *    ],\n *    \"emojis\": [\n *      smiley: {\n *        added_in: \"6.0\",\n *        emoticons: [\"=)\", \"=-)\"],\n *        has_img_apple: true,\n *        has_img_facebook: true,\n *        has_img_google: true,\n *        has_img_twitter: true,\n *        keywords: [\"face\", \"happy\", \"joy\", \"haha\", \":D\", \":)\", \"smile\", \"funny\"],\n *        name: \"Smiling Face with Open Mouth\",\n *        non_qualified: undefined,\n *        search: \"smiley,smiling,face,with,open,mouth,happy,joy,haha,:d,:),smile,funny,=),=-)\",\n *        sheet_x: 30,\n *        sheet_y: 27,\n *        short_names: [\"smiley\"],\n *        text: \":)\",\n *        unified: \"1F603\",\n *      }, {\n *      +1: {    // emoji with skin_variations\n *          ..., // all the regular fields are present\n *          name: \"Thumbs Up Sign\",\n *          short_names: (2) [\"+1\", \"thumbsup\"],\n *          skin_variations: {\n *            1F3FB:             // each variation has additional set of fields:\n *              added_in: \"8.0\",\n *              has_img_apple: true,\n *              has_img_facebook: true,\n *              has_img_google: true,\n *              has_img_twitter: true,\n *              image: \"1f44d-1f3fb.png\",\n *              non_qualified: null,\n *              sheet_x: 14,\n *              sheet_y: 50,\n *              unified: \"1F44D-1F3FB\",\n *            1F3FB: {…},\n *            1F3FC: {…},\n *            1F3FD: {…},\n *            1F3FE: {…},\n *            1F3FF: {…}\n *            },\n *          ...\n *      },\n *      a: {  // emoji with non_qualified field set\n *        added_in: \"6.0\",\n *        emoticons: undefined,\n *        has_img_apple: true,\n *        ...\n *        non_qualified: \"1F170\",\n *        unified: \"1F170-FE0F\",\n *     },\n *     ...\n *   ]\n * }\n */\n\n/**\n * Wraps raw jason emoji data, serves as data source for\n * emoji picker components.\n *\n * Usage:\n *\n *   import data from '../data/all.json'\n *   let index = new EmojiIndex(data)\n *\n */\nexport class EmojiIndex {\n  /**\n   * Constructor.\n   *\n   * @param {object} data - Raw json data, see the structure above.\n   * @param {object} options - additional options, as an object:\n   * @param {Function} emojisToShowFilter - optional, function to filter out\n   *   some emojis, function(emoji) { return true|false }\n   *   where `emoji` is an raw emoji object, see data.emojis above.\n   * @param {Array} include - optional, a list of category ids to include.\n   * @param {Array} exclude - optional, a list of category ids to exclude.\n   * @param {Array} custom - optional, a list custom emojis, each emoji is\n   *   an object, see data.emojis above for examples.\n   */\n  constructor(\n    data,\n    {\n      emojisToShowFilter,\n      include,\n      exclude,\n      custom,\n      recent,\n      recentLength = 20,\n    } = {},\n  ) {\n    this._data = uncompress(data)\n    // Callback to exclude specific emojis\n    this._emojisFilter = emojisToShowFilter || null\n    // Categories to include / exclude\n    this._include = include || null\n    this._exclude = exclude || null\n    // Custom emojis\n    this._custom = custom || []\n    // Recent emojis\n    // TODO: make parameter configurable\n    this._recent = recent || frequently.get(recentLength)\n\n    this._emojis = {}\n    this._nativeEmojis = {}\n    this._emoticons = {}\n\n    this._categories = []\n    this._recentCategory = { id: 'recent', name: 'Recent', emojis: [] }\n    this._customCategory = { id: 'custom', name: 'Custom', emojis: [] }\n    this._searchIndex = {}\n    this.buildIndex()\n    Object.freeze(this)\n  }\n\n  buildIndex() {\n    let allCategories = this._data.categories\n\n    if (this._include) {\n      // Remove categories that are not in the include list.\n      allCategories = allCategories.filter((item) => {\n        return this._include.includes(item.id)\n      })\n      // Sort categories according to the include list.\n      allCategories = allCategories.sort((a, b) => {\n        const indexA = this._include.indexOf(a.id)\n        const indexB = this._include.indexOf(b.id)\n        if (indexA < indexB) {\n          return -1\n        }\n        if (indexA > indexB) {\n          return 1\n        }\n        return 0\n      })\n    }\n\n    allCategories.forEach((categoryData) => {\n      if (!this.isCategoryNeeded(categoryData.id)) {\n        return\n      }\n      let category = {\n        id: categoryData.id,\n        name: categoryData.name,\n        emojis: [],\n      }\n      categoryData.emojis.forEach((emojiId) => {\n        let emoji = this.addEmoji(emojiId)\n        if (emoji) {\n          category.emojis.push(emoji)\n        }\n      })\n      if (category.emojis.length) {\n        this._categories.push(category)\n      }\n    })\n\n    if (this.isCategoryNeeded('custom')) {\n      if (this._custom.length > 0) {\n        for (let customEmoji of this._custom) {\n          this.addCustomEmoji(customEmoji)\n        }\n      }\n      if (this._customCategory.emojis.length) {\n        this._categories.push(this._customCategory)\n      }\n    }\n\n    if (this.isCategoryNeeded('recent')) {\n      if (this._recent.length) {\n        this._recent.map((id) => {\n          for (let customEmoji of this._customCategory.emojis) {\n            if (customEmoji.id === id) {\n              this._recentCategory.emojis.push(customEmoji)\n              return\n            }\n          }\n          if (this.hasEmoji(id)) {\n            this._recentCategory.emojis.push(this.emoji(id))\n          }\n          return\n        })\n      }\n      // Add recent category to the top\n      if (this._recentCategory.emojis.length) {\n        this._categories.unshift(this._recentCategory)\n      }\n    }\n  }\n\n  /**\n   * Find the emoji from the string\n   */\n  findEmoji(emoji, skin) {\n    // 1. Parse as :emoji_name:skin-tone-xx:\n    let matches = emoji.match(COLONS_REGEX)\n\n    if (matches) {\n      emoji = matches[1]\n      if (matches[2]) {\n        skin = parseInt(matches[2], 10)\n      }\n    }\n\n    // 2. Check if the specified emoji is an alias\n    if (this._data.aliases.hasOwnProperty(emoji)) {\n      emoji = this._data.aliases[emoji]\n    }\n\n    // 3. Check if we have the specified emoji\n    if (this._emojis.hasOwnProperty(emoji)) {\n      let emojiObject = this._emojis[emoji]\n      if (skin) {\n        return emojiObject.getSkin(skin)\n      }\n      return emojiObject\n    }\n\n    // 4. Check if we have the specified native emoji\n    if (this._nativeEmojis.hasOwnProperty(emoji)) {\n      return this._nativeEmojis[emoji]\n    }\n    return null\n  }\n\n  categories() {\n    return this._categories\n  }\n\n  emoji(emojiId) {\n    if (this._data.aliases.hasOwnProperty(emojiId)) {\n      emojiId = this._data.aliases[emojiId]\n    }\n    let emoji = this._emojis[emojiId]\n    if (!emoji) {\n      throw new Error('Can not find emoji by id: ' + emojiId)\n    }\n    return emoji\n  }\n\n  firstEmoji() {\n    let emoji = this._emojis[Object.keys(this._emojis)[0]]\n    if (!emoji) {\n      throw new Error('Can not get first emoji')\n    }\n    return emoji\n  }\n\n  hasEmoji(emojiId) {\n    if (this._data.aliases.hasOwnProperty(emojiId)) {\n      emojiId = this._data.aliases[emojiId]\n    }\n    if (this._emojis[emojiId]) {\n      return true\n    }\n    return false\n  }\n\n  nativeEmoji(unicodeEmoji) {\n    if (this._nativeEmojis.hasOwnProperty(unicodeEmoji)) {\n      return this._nativeEmojis[unicodeEmoji]\n    }\n    return null\n  }\n\n  search(value, maxResults) {\n    maxResults || (maxResults = 75)\n    if (!value.length) {\n      return null\n    }\n    if (value == '-' || value == '-1') {\n      return [this.emoji('-1')]\n    }\n\n    let values = value.toLowerCase().split(/[\\s|,|\\-|_]+/)\n    let allResults = []\n\n    if (values.length > 2) {\n      values = [values[0], values[1]]\n    }\n\n    allResults = values\n      .map((value) => {\n        // Start searchin in the global list of emojis\n        let emojis = this._emojis\n        let currentIndex = this._searchIndex\n        let length = 0\n\n        for (let charIndex = 0; charIndex < value.length; charIndex++) {\n          const char = value[charIndex]\n          length++\n\n          currentIndex[char] || (currentIndex[char] = {})\n          currentIndex = currentIndex[char]\n\n          if (!currentIndex.results) {\n            let scores = {}\n            currentIndex.results = []\n            currentIndex.emojis = {}\n\n            for (let emojiId in emojis) {\n              let emoji = emojis[emojiId]\n              // search is a comma-separated string with words, related\n              // to the emoji, for example:\n              // search: \"smiley,smiling,face,joy,haha,:d,:),smile,funny,=),=-)\",\n              let search = emoji._data.search\n              let sub = value.substr(0, length)\n              let subIndex = search.indexOf(sub)\n              if (subIndex != -1) {\n                let score = subIndex + 1\n                if (sub == emojiId) score = 0\n\n                currentIndex.results.push(emoji)\n                currentIndex.emojis[emojiId] = emoji\n\n                scores[emojiId] = score\n              }\n            }\n            currentIndex.results.sort((a, b) => {\n              var aScore = scores[a.id],\n                bScore = scores[b.id]\n              return aScore - bScore\n            })\n          }\n\n          // continue search in the reduced set of emojis\n          emojis = currentIndex.emojis\n        }\n        return currentIndex.results\n        // The \"filter\" call removes undefined values from allResults\n        // array, for example, if we have \"test \" (with trailing space),\n        // we will get \"[Array, undefined]\" for allResults and after\n        // the \"filter\" call it will turn into \"[Array]\"\n      })\n      .filter((a) => a)\n\n    var results = null\n    if (allResults.length > 1) {\n      results = intersect.apply(null, allResults)\n    } else if (allResults.length) {\n      results = allResults[0]\n    } else {\n      results = []\n    }\n    if (results && results.length > maxResults) {\n      results = results.slice(0, maxResults)\n    }\n    return results\n  }\n\n  addCustomEmoji(customEmoji) {\n    let emojiData = Object.assign({}, customEmoji, {\n      id: customEmoji.short_names[0],\n      custom: true,\n    })\n    if (!emojiData.search) {\n      emojiData.search = buildSearch(emojiData)\n    }\n    let emoji = new EmojiData(emojiData)\n    this._emojis[emoji.id] = emoji\n    this._customCategory.emojis.push(emoji)\n    return emoji\n  }\n\n  addEmoji(emojiId) {\n    // We expect the correct emoji id that is present in the emojis data.\n    let data = this._data.emojis[emojiId]\n\n    if (!this.isEmojiNeeded(data)) {\n      return false\n    }\n\n    let emoji = new EmojiData(data)\n    this._emojis[emojiId] = emoji\n    if (emoji.native) {\n      this._nativeEmojis[emoji.native] = emoji\n    }\n    if (emoji._skins) {\n      for (let idx in emoji._skins) {\n        let skin = emoji._skins[idx]\n        if (skin.native) {\n          this._nativeEmojis[skin.native] = skin\n        }\n      }\n    }\n\n    if (emoji.emoticons) {\n      emoji.emoticons.forEach((emoticon) => {\n        if (this._emoticons[emoticon]) {\n          return\n        }\n        this._emoticons[emoticon] = emojiId\n      })\n    }\n    return emoji\n  }\n\n  /**\n   * Check if we need to include given category.\n   *\n   * @param {string} category_id - The category id.\n   * @return {boolean} - Whether to include the emoji.\n   */\n  isCategoryNeeded(category_id) {\n    let isIncluded =\n      this._include && this._include.length\n        ? this._include.indexOf(category_id) > -1\n        : true\n    let isExcluded =\n      this._exclude && this._exclude.length\n        ? this._exclude.indexOf(category_id) > -1\n        : false\n    if (!isIncluded || isExcluded) {\n      return false\n    }\n    return true\n  }\n\n  /**\n   * Check if we need to include given emoji.\n   *\n   * @param {object} emoji - The raw emoji object.\n   * @return {boolean} - Whether to include the emoji.\n   */\n  isEmojiNeeded(emoji) {\n    if (this._emojisFilter) {\n      return this._emojisFilter(emoji)\n    }\n    return true\n  }\n}\n\nexport class EmojiData {\n  constructor(data) {\n    this._data = Object.assign({}, data)\n    this._skins = null\n    if (this._data.skin_variations) {\n      this._skins = []\n      for (var skinIdx in SKINS) {\n        let skinKey = SKINS[skinIdx]\n        let variationData = this._data.skin_variations[skinKey]\n        let skinData = Object.assign({}, data)\n        for (let k in variationData) {\n          skinData[k] = variationData[k]\n        }\n        delete skinData.skin_variations\n        skinData['skin_tone'] = parseInt(skinIdx) + 1\n        this._skins.push(new EmojiData(skinData))\n      }\n    }\n    this._sanitized = sanitize(this._data)\n    for (let key in this._sanitized) {\n      this[key] = this._sanitized[key]\n    }\n    this.short_names = this._data.short_names\n    this.short_name = this._data.short_names[0]\n    Object.freeze(this)\n  }\n\n  getSkin(skinIdx) {\n    if (skinIdx && skinIdx != 'native' && this._skins) {\n      return this._skins[skinIdx - 1]\n    }\n    return this\n  }\n\n  getPosition() {\n    let adjustedColumns = SHEET_COLUMNS - 1,\n      x = +((100 / adjustedColumns) * this._data.sheet_x).toFixed(2),\n      y = +((100 / adjustedColumns) * this._data.sheet_y).toFixed(2)\n    return `${x}% ${y}%`\n  }\n\n  ariaLabel() {\n    return [this.native].concat(this.short_names).filter(Boolean).join(', ')\n  }\n}\n\nexport class EmojiView {\n  /**\n   * emoji - Emoji to display\n   * set - string, emoji set name\n   * native - boolean, whether to render native emoji\n   * fallback - fallback function to render missing emoji, optional\n   * emojiTooltip - wether we need to show the emoji tooltip, optional\n   * emojiSize - emoji size in pixels, optional\n   */\n  constructor(emoji, skin, set, native, fallback, emojiTooltip, emojiSize) {\n    this._emoji = emoji\n    this._native = native\n    this._skin = skin\n    this._set = set\n    this._fallback = fallback\n\n    this.canRender = this._canRender()\n    this.cssClass = this._cssClass()\n    this.cssStyle = this._cssStyle(emojiSize)\n    this.content = this._content()\n    this.title = emojiTooltip === true ? emoji.short_name : null\n    this.ariaLabel = emoji.ariaLabel()\n\n    Object.freeze(this)\n  }\n\n  getEmoji() {\n    return this._emoji.getSkin(this._skin)\n  }\n\n  _canRender() {\n    return (\n      this._isCustom() || this._isNative() || this._hasEmoji() || this._fallback\n    )\n  }\n\n  _cssClass() {\n    return ['emoji-set-' + this._set, 'emoji-type-' + this._emojiType()]\n  }\n\n  _cssStyle(emojiSize) {\n    let cssStyle = {}\n    if (this._isCustom()) {\n      cssStyle = {\n        backgroundImage: 'url(' + this.getEmoji()._data.imageUrl + ')',\n        backgroundSize: '100%',\n        width: emojiSize + 'px',\n        height: emojiSize + 'px',\n      }\n    } else if (this._hasEmoji() && !this._isNative()) {\n      cssStyle = {\n        backgroundPosition: this.getEmoji().getPosition(),\n      }\n    }\n    if (emojiSize) {\n      if (this._isNative()) {\n        // Set font-size for native emoji.\n        cssStyle = Object.assign(cssStyle, {\n          // font-size is used for native emoji which we need\n          // to scale with 0.95 factor to have them look approximately\n          // the same size as image-based emoji.\n          fontSize: Math.round(emojiSize * 0.95 * 10) / 10 + 'px',\n        })\n      } else {\n        // Set width/height for image emoji.\n        cssStyle = Object.assign(cssStyle, {\n          width: emojiSize + 'px',\n          height: emojiSize + 'px',\n        })\n      }\n    }\n    return cssStyle\n  }\n\n  _content() {\n    if (this._isCustom()) {\n      return ''\n    }\n    if (this._isNative()) {\n      return this.getEmoji().native\n    }\n    if (this._hasEmoji()) {\n      return ''\n    }\n    return this._fallback ? this._fallback(this.getEmoji()) : null\n  }\n\n  _isNative() {\n    return this._native\n  }\n\n  _isCustom() {\n    return this.getEmoji().custom\n  }\n\n  _hasEmoji() {\n    if (!this.getEmoji()._data) {\n      // Return false if we have no data.\n      return false\n    }\n    const hasImage = this.getEmoji()._data['has_img_' + this._set]\n    if (hasImage === undefined) {\n      // If there is no has_img_xxx in the data, we are working with\n      // specific data file, like facebook.json, so we assume all\n      // emojis are available (the :set setting for picker should\n      // match the data file).\n      return true\n    }\n    // Otherwise, we are using all.json and can switch between different\n    // sets - in this case the `has_img_{set_name}` is a boolean that\n    // indicates if there is such image or not for a given set.\n    return hasImage\n  }\n\n  _emojiType() {\n    if (this._isCustom()) {\n      return 'custom'\n    }\n    if (this._isNative()) {\n      return 'native'\n    }\n    if (this._hasEmoji()) {\n      return 'image'\n    }\n    return 'fallback'\n  }\n}\n\nexport function sanitize(emoji) {\n  var {\n      name,\n      short_names,\n      skin_tone,\n      skin_variations,\n      emoticons,\n      unified,\n      custom,\n      imageUrl,\n    } = emoji,\n    id = emoji.id || short_names[0],\n    colons = `:${id}:`\n\n  if (custom) {\n    return {\n      id,\n      name,\n      colons,\n      emoticons,\n      custom,\n      imageUrl,\n    }\n  }\n\n  if (skin_tone) {\n    colons += `:skin-tone-${skin_tone}:`\n  }\n\n  return {\n    id,\n    name,\n    colons,\n    emoticons,\n    unified: unified.toLowerCase(),\n    skin: skin_tone || (skin_variations ? 1 : null),\n    native: unifiedToNative(unified),\n  }\n}\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAEhB,IAAM,QAAQ;AAEd,IAAI,0BACF,OAAO,WAAW,eAAe,kBAAkB;AAErD,IAAI;AACJ,IAAI;AAEJ,SAAS,YAAY,UAAU;AAC7B,eAAa,WAAW,CAAC;AAEzB,WAAS,SAAS;AAClB,WAAS,SAAS;AACpB;AAEA,SAAS,aAAa,WAAW;AAC/B,cAAY;AACd;AAEA,SAAS,OAAO,OAAO;AACrB,WAAS,OAAO,OAAO;AACrB,QAAI,QAAQ,MAAM,GAAG;AACrB,QAAI,KAAK,KAAK;AAAA,EAChB;AACF;AAEA,SAAS,IAAI,KAAK,OAAO;AACvB,MAAI,QAAQ;AACV,WAAO,KAAK,KAAK;AAAA,EACnB,OAAO;AACL,QAAI,CAAC,wBAAyB;AAC9B,QAAI;AACF,aAAO,aAAa,GAAG,SAAS,IAAI,GAAG,EAAE,IAAI,MAAM,UAAU,KAAK;AAAA,IACpE,SAAS,GAAG;AAAA,IAAC;AAAA,EACf;AACF;AAEA,SAAS,IAAI,KAAK;AAChB,MAAI,QAAQ;AACV,WAAO,OAAO,GAAG;AAAA,EACnB,OAAO;AACL,QAAI,CAAC,wBAAyB;AAC9B,QAAI;AACF,UAAI,QAAQ,OAAO,aAAa,GAAG,SAAS,IAAI,GAAG,EAAE;AAAA,IACvD,SAAS,GAAG;AACV;AAAA,IACF;AAEA,QAAI,OAAO;AACT,aAAO,KAAK,MAAM,KAAK;AAAA,IACzB;AAAA,EACF;AACF;AAEA,IAAO,gBAAQ,EAAE,QAAQ,KAAK,KAAK,cAAc,YAAY;;;ACxD7D,IAAM,UAAU;AAAA,EACd,MAAM;AAAA,EACN,SAAS;AAAA,EACT,eAAe;AAAA,EACf,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,UAAU;AACZ;AAEA,IAAM,cAAc,CAAC,UAAU;AAC7B,QAAM,SAAS,CAAC;AAEhB,MAAI,cAAc,CAAC,SAAS,UAAU;AACpC,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAEA;AAAC,KAAC,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO,GAAG,QAAQ,CAAC,WAAW;AAClE;AAAC,OAAC,QAAQ,OAAO,MAAM,WAAW,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM;AAC7D,YAAI,EAAE,YAAY;AAElB,YAAI,OAAO,QAAQ,CAAC,KAAK,IAAI;AAC3B,iBAAO,KAAK,CAAC;AAAA,QACf;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEA,cAAY,MAAM,aAAa,IAAI;AACnC,cAAY,MAAM,MAAM,IAAI;AAC5B,cAAY,MAAM,UAAU,KAAK;AACjC,cAAY,MAAM,WAAW,KAAK;AAElC,SAAO,OAAO,KAAK,GAAG;AACxB;AAEA,SAAS,WAAW,QAAQ;AAE1B,MAAI,YAAY,OAAO,oBAAoB,MAAM;AAGjD,WAAS,QAAQ,WAAW;AAC1B,QAAI,QAAQ,OAAO,IAAI;AACvB,WAAO,IAAI,IACT,SAAS,OAAO,UAAU,WAAW,WAAW,KAAK,IAAI;AAAA,EAC7D;AACA,SAAO,OAAO,OAAO,MAAM;AAC7B;AAEA,IAAM,aAAa,CAAC,SAAS;AAC3B,MAAI,CAAC,KAAK,YAAY;AACpB,WAAO;AAAA,EACT;AACA,OAAK,aAAa;AAElB,WAAS,MAAM,KAAK,QAAQ;AAC1B,QAAI,QAAQ,KAAK,OAAO,EAAE;AAE1B,aAAS,OAAO,SAAS;AACvB,YAAM,GAAG,IAAI,MAAM,QAAQ,GAAG,CAAC;AAC/B,aAAO,MAAM,QAAQ,GAAG,CAAC;AAAA,IAC3B;AAEA,QAAI,CAAC,MAAM,YAAa,OAAM,cAAc,CAAC;AAC7C,UAAM,YAAY,QAAQ,EAAE;AAE5B,UAAM,UAAU,MAAM,MAAM,CAAC;AAC7B,UAAM,UAAU,MAAM,MAAM,CAAC;AAC7B,WAAO,MAAM;AAEb,QAAI,CAAC,MAAM,KAAM,OAAM,OAAO;AAE9B,QAAI,CAAC,MAAM,SAAU,OAAM,WAAW;AACtC,UAAM,WAAW,MAAM,SAAS,QAAQ,CAAC;AAEzC,UAAM,SAAS,YAAY,KAAK;AAAA,EAClC;AACA,SAAO,WAAW,IAAI;AACtB,SAAO;AACT;;;ACpFA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAI;AAAJ,IAAgB;AAChB,IAAI,WAAW,CAAC;AAEhB,SAAS,OAAO;AACd,gBAAc;AACd,eAAa,cAAM,IAAI,YAAY;AACrC;AAEA,SAAS,IAAI,OAAO;AAClB,MAAI,CAAC,YAAa,MAAK;AACvB,MAAI,EAAE,GAAG,IAAI;AAEb,iBAAe,aAAa;AAC5B,aAAW,EAAE,MAAM,WAAW,EAAE,IAAI;AACpC,aAAW,EAAE,KAAK;AAElB,gBAAM,IAAI,QAAQ,EAAE;AACpB,gBAAM,IAAI,cAAc,UAAU;AACpC;AAEA,SAASA,KAAI,WAAW;AACtB,MAAI,CAAC,YAAa,MAAK;AACvB,MAAI,CAAC,YAAY;AACf,eAAW,CAAC;AAEZ,UAAM,SAAS,CAAC;AAEhB,QAAI,gBAAgB,KAAK,IAAI,WAAW,SAAS,MAAM;AACvD,aAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,eAAS,SAAS,CAAC,CAAC,IAAI,UAAU,gBAAgB,KAAK,GAAG,EAAE,IAAI;AAChE,aAAO,KAAK,SAAS,CAAC,CAAC;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AAEA,QAAM,WAAW;AACjB,QAAM,iBAAiB,CAAC;AAExB,WAAS,OAAO,YAAY;AAC1B,QAAI,WAAW,eAAe,GAAG,GAAG;AAClC,qBAAe,KAAK,GAAG;AAAA,IACzB;AAAA,EACF;AAEA,QAAM,SAAS,eACZ,KAAK,CAAC,GAAG,MAAM,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC,EAC5C,QAAQ;AACX,QAAM,SAAS,OAAO,MAAM,GAAG,QAAQ;AAEvC,QAAM,OAAO,cAAM,IAAI,MAAM;AAE7B,MAAI,QAAQ,OAAO,QAAQ,IAAI,KAAK,IAAI;AACtC,WAAO,IAAI;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,SAAO;AACT;AAEA,IAAO,qBAAQ,EAAE,KAAK,KAAAA,KAAI;;;ACjF1B,SAAoB,WAAXC,gBAA0B;AACnC,SAAoB,WAAXA,gBAA2B;AACpC,SAAoB,WAAXA,gBAA0B;AACnC,SAAoB,WAAXA,gBAAyB;AAClC,SAAoB,WAAXA,gBAAwB;AACjC,SAAoB,WAAXA,gBAAwB;AACjC,SAAoB,WAAXA,gBAAyB;;;ACNlC,IAAM,UAAU;AAEhB,IAAO,8BAAQ,QAAQ,iBACrB,SAAS,sBAAsB;AAC7B,MAAI,WAAW;AACf,MAAI,YAAY,CAAC;AACjB,MAAI;AACJ,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,SAAS,UAAU;AACvB,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AACb,SAAO,EAAE,QAAQ,QAAQ;AACvB,QAAI,YAAY,OAAO,UAAU,KAAK,CAAC;AACvC,QACE,CAAC,SAAS,SAAS;AAAA,IACnB,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,KAAK,MAAM,SAAS,KAAK,WACzB;AACA,YAAM,WAAW,yBAAyB,SAAS;AAAA,IACrD;AACA,QAAI,aAAa,OAAQ;AAEvB,gBAAU,KAAK,SAAS;AAAA,IAC1B,OAAO;AAGL,mBAAa;AACb,uBAAiB,aAAa,MAAM;AACpC,qBAAgB,YAAY,OAAS;AACrC,gBAAU,KAAK,eAAe,YAAY;AAAA,IAC5C;AACA,QAAI,QAAQ,MAAM,UAAU,UAAU,SAAS,UAAU;AACvD,gBAAU,OAAO,aAAa,MAAM,MAAM,SAAS;AACnD,gBAAU,SAAS;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AACT;;;ACvCF,SAAS,gBAAgB,SAAS;AAChC,MAAI,WAAW,QAAQ,MAAM,GAAG,GAC9B,aAAa,SAAS,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;AAE3C,SAAO,4BAAoB,MAAM,MAAM,UAAU;AACnD;AAEA,SAAS,KAAK,KAAK;AACjB,SAAO,IAAI,OAAO,CAAC,KAAK,SAAS;AAC/B,QAAI,IAAI,QAAQ,IAAI,MAAM,IAAI;AAC5B,UAAI,KAAK,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,SAAS,UAAU,GAAG,GAAG;AACvB,QAAM,QAAQ,KAAK,CAAC;AACpB,QAAM,QAAQ,KAAK,CAAC;AAEpB,SAAO,MAAM,OAAO,CAAC,SAAS,MAAM,QAAQ,IAAI,KAAK,CAAC;AACxD;;;ACnBA,IAAM,gBAAgB;AACtB,IAAM,eAAe;AAErB,IAAM,QAAQ,CAAC,SAAS,SAAS,SAAS,SAAS,SAAS,OAAO;AAyF5D,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EActB,YACE,MACA;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,EACjB,IAAI,CAAC,GACL;AACA,SAAK,QAAQ,WAAW,IAAI;AAE5B,SAAK,gBAAgB,sBAAsB;AAE3C,SAAK,WAAW,WAAW;AAC3B,SAAK,WAAW,WAAW;AAE3B,SAAK,UAAU,UAAU,CAAC;AAG1B,SAAK,UAAU,UAAU,mBAAW,IAAI,YAAY;AAEpD,SAAK,UAAU,CAAC;AAChB,SAAK,gBAAgB,CAAC;AACtB,SAAK,aAAa,CAAC;AAEnB,SAAK,cAAc,CAAC;AACpB,SAAK,kBAAkB,EAAE,IAAI,UAAU,MAAM,UAAU,QAAQ,CAAC,EAAE;AAClE,SAAK,kBAAkB,EAAE,IAAI,UAAU,MAAM,UAAU,QAAQ,CAAC,EAAE;AAClE,SAAK,eAAe,CAAC;AACrB,SAAK,WAAW;AAChB,WAAO,OAAO,IAAI;AAAA,EACpB;AAAA,EAEA,aAAa;AACX,QAAI,gBAAgB,KAAK,MAAM;AAE/B,QAAI,KAAK,UAAU;AAEjB,sBAAgB,cAAc,OAAO,CAAC,SAAS;AAC7C,eAAO,KAAK,SAAS,SAAS,KAAK,EAAE;AAAA,MACvC,CAAC;AAED,sBAAgB,cAAc,KAAK,CAAC,GAAG,MAAM;AAC3C,cAAM,SAAS,KAAK,SAAS,QAAQ,EAAE,EAAE;AACzC,cAAM,SAAS,KAAK,SAAS,QAAQ,EAAE,EAAE;AACzC,YAAI,SAAS,QAAQ;AACnB,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,QAAQ;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,kBAAc,QAAQ,CAAC,iBAAiB;AACtC,UAAI,CAAC,KAAK,iBAAiB,aAAa,EAAE,GAAG;AAC3C;AAAA,MACF;AACA,UAAI,WAAW;AAAA,QACb,IAAI,aAAa;AAAA,QACjB,MAAM,aAAa;AAAA,QACnB,QAAQ,CAAC;AAAA,MACX;AACA,mBAAa,OAAO,QAAQ,CAAC,YAAY;AACvC,YAAI,QAAQ,KAAK,SAAS,OAAO;AACjC,YAAI,OAAO;AACT,mBAAS,OAAO,KAAK,KAAK;AAAA,QAC5B;AAAA,MACF,CAAC;AACD,UAAI,SAAS,OAAO,QAAQ;AAC1B,aAAK,YAAY,KAAK,QAAQ;AAAA,MAChC;AAAA,IACF,CAAC;AAED,QAAI,KAAK,iBAAiB,QAAQ,GAAG;AACnC,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,iBAAS,eAAe,KAAK,SAAS;AACpC,eAAK,eAAe,WAAW;AAAA,QACjC;AAAA,MACF;AACA,UAAI,KAAK,gBAAgB,OAAO,QAAQ;AACtC,aAAK,YAAY,KAAK,KAAK,eAAe;AAAA,MAC5C;AAAA,IACF;AAEA,QAAI,KAAK,iBAAiB,QAAQ,GAAG;AACnC,UAAI,KAAK,QAAQ,QAAQ;AACvB,aAAK,QAAQ,IAAI,CAAC,OAAO;AACvB,mBAAS,eAAe,KAAK,gBAAgB,QAAQ;AACnD,gBAAI,YAAY,OAAO,IAAI;AACzB,mBAAK,gBAAgB,OAAO,KAAK,WAAW;AAC5C;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,SAAS,EAAE,GAAG;AACrB,iBAAK,gBAAgB,OAAO,KAAK,KAAK,MAAM,EAAE,CAAC;AAAA,UACjD;AACA;AAAA,QACF,CAAC;AAAA,MACH;AAEA,UAAI,KAAK,gBAAgB,OAAO,QAAQ;AACtC,aAAK,YAAY,QAAQ,KAAK,eAAe;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,OAAO,MAAM;AAErB,QAAI,UAAU,MAAM,MAAM,YAAY;AAEtC,QAAI,SAAS;AACX,cAAQ,QAAQ,CAAC;AACjB,UAAI,QAAQ,CAAC,GAAG;AACd,eAAO,SAAS,QAAQ,CAAC,GAAG,EAAE;AAAA,MAChC;AAAA,IACF;AAGA,QAAI,KAAK,MAAM,QAAQ,eAAe,KAAK,GAAG;AAC5C,cAAQ,KAAK,MAAM,QAAQ,KAAK;AAAA,IAClC;AAGA,QAAI,KAAK,QAAQ,eAAe,KAAK,GAAG;AACtC,UAAI,cAAc,KAAK,QAAQ,KAAK;AACpC,UAAI,MAAM;AACR,eAAO,YAAY,QAAQ,IAAI;AAAA,MACjC;AACA,aAAO;AAAA,IACT;AAGA,QAAI,KAAK,cAAc,eAAe,KAAK,GAAG;AAC5C,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,MAAM,SAAS;AACb,QAAI,KAAK,MAAM,QAAQ,eAAe,OAAO,GAAG;AAC9C,gBAAU,KAAK,MAAM,QAAQ,OAAO;AAAA,IACtC;AACA,QAAI,QAAQ,KAAK,QAAQ,OAAO;AAChC,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,+BAA+B,OAAO;AAAA,IACxD;AACA,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,QAAI,QAAQ,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO,EAAE,CAAC,CAAC;AACrD,QAAI,CAAC,OAAO;AACV,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC3C;AACA,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,SAAS;AAChB,QAAI,KAAK,MAAM,QAAQ,eAAe,OAAO,GAAG;AAC9C,gBAAU,KAAK,MAAM,QAAQ,OAAO;AAAA,IACtC;AACA,QAAI,KAAK,QAAQ,OAAO,GAAG;AACzB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EAEA,YAAY,cAAc;AACxB,QAAI,KAAK,cAAc,eAAe,YAAY,GAAG;AACnD,aAAO,KAAK,cAAc,YAAY;AAAA,IACxC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,OAAO,OAAO,YAAY;AACxB,mBAAe,aAAa;AAC5B,QAAI,CAAC,MAAM,QAAQ;AACjB,aAAO;AAAA,IACT;AACA,QAAI,SAAS,OAAO,SAAS,MAAM;AACjC,aAAO,CAAC,KAAK,MAAM,IAAI,CAAC;AAAA,IAC1B;AAEA,QAAI,SAAS,MAAM,YAAY,EAAE,MAAM,cAAc;AACrD,QAAI,aAAa,CAAC;AAElB,QAAI,OAAO,SAAS,GAAG;AACrB,eAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;AAAA,IAChC;AAEA,iBAAa,OACV,IAAI,CAACC,WAAU;AAEd,UAAI,SAAS,KAAK;AAClB,UAAI,eAAe,KAAK;AACxB,UAAI,SAAS;AAEb,eAAS,YAAY,GAAG,YAAYA,OAAM,QAAQ,aAAa;AAC7D,cAAM,OAAOA,OAAM,SAAS;AAC5B;AAEA,qBAAa,IAAI,MAAM,aAAa,IAAI,IAAI,CAAC;AAC7C,uBAAe,aAAa,IAAI;AAEhC,YAAI,CAAC,aAAa,SAAS;AACzB,cAAI,SAAS,CAAC;AACd,uBAAa,UAAU,CAAC;AACxB,uBAAa,SAAS,CAAC;AAEvB,mBAAS,WAAW,QAAQ;AAC1B,gBAAI,QAAQ,OAAO,OAAO;AAI1B,gBAAI,SAAS,MAAM,MAAM;AACzB,gBAAI,MAAMA,OAAM,OAAO,GAAG,MAAM;AAChC,gBAAI,WAAW,OAAO,QAAQ,GAAG;AACjC,gBAAI,YAAY,IAAI;AAClB,kBAAI,QAAQ,WAAW;AACvB,kBAAI,OAAO,QAAS,SAAQ;AAE5B,2BAAa,QAAQ,KAAK,KAAK;AAC/B,2BAAa,OAAO,OAAO,IAAI;AAE/B,qBAAO,OAAO,IAAI;AAAA,YACpB;AAAA,UACF;AACA,uBAAa,QAAQ,KAAK,CAAC,GAAG,MAAM;AAClC,gBAAI,SAAS,OAAO,EAAE,EAAE,GACtB,SAAS,OAAO,EAAE,EAAE;AACtB,mBAAO,SAAS;AAAA,UAClB,CAAC;AAAA,QACH;AAGA,iBAAS,aAAa;AAAA,MACxB;AACA,aAAO,aAAa;AAAA,IAKtB,CAAC,EACA,OAAO,CAAC,MAAM,CAAC;AAElB,QAAI,UAAU;AACd,QAAI,WAAW,SAAS,GAAG;AACzB,gBAAU,UAAU,MAAM,MAAM,UAAU;AAAA,IAC5C,WAAW,WAAW,QAAQ;AAC5B,gBAAU,WAAW,CAAC;AAAA,IACxB,OAAO;AACL,gBAAU,CAAC;AAAA,IACb;AACA,QAAI,WAAW,QAAQ,SAAS,YAAY;AAC1C,gBAAU,QAAQ,MAAM,GAAG,UAAU;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,eAAe,aAAa;AAC1B,QAAI,YAAY,OAAO,OAAO,CAAC,GAAG,aAAa;AAAA,MAC7C,IAAI,YAAY,YAAY,CAAC;AAAA,MAC7B,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,CAAC,UAAU,QAAQ;AACrB,gBAAU,SAAS,YAAY,SAAS;AAAA,IAC1C;AACA,QAAI,QAAQ,IAAI,UAAU,SAAS;AACnC,SAAK,QAAQ,MAAM,EAAE,IAAI;AACzB,SAAK,gBAAgB,OAAO,KAAK,KAAK;AACtC,WAAO;AAAA,EACT;AAAA,EAEA,SAAS,SAAS;AAEhB,QAAI,OAAO,KAAK,MAAM,OAAO,OAAO;AAEpC,QAAI,CAAC,KAAK,cAAc,IAAI,GAAG;AAC7B,aAAO;AAAA,IACT;AAEA,QAAI,QAAQ,IAAI,UAAU,IAAI;AAC9B,SAAK,QAAQ,OAAO,IAAI;AACxB,QAAI,MAAM,QAAQ;AAChB,WAAK,cAAc,MAAM,MAAM,IAAI;AAAA,IACrC;AACA,QAAI,MAAM,QAAQ;AAChB,eAAS,OAAO,MAAM,QAAQ;AAC5B,YAAI,OAAO,MAAM,OAAO,GAAG;AAC3B,YAAI,KAAK,QAAQ;AACf,eAAK,cAAc,KAAK,MAAM,IAAI;AAAA,QACpC;AAAA,MACF;AAAA,IACF;AAEA,QAAI,MAAM,WAAW;AACnB,YAAM,UAAU,QAAQ,CAAC,aAAa;AACpC,YAAI,KAAK,WAAW,QAAQ,GAAG;AAC7B;AAAA,QACF;AACA,aAAK,WAAW,QAAQ,IAAI;AAAA,MAC9B,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,aAAa;AAC5B,QAAI,aACF,KAAK,YAAY,KAAK,SAAS,SAC3B,KAAK,SAAS,QAAQ,WAAW,IAAI,KACrC;AACN,QAAI,aACF,KAAK,YAAY,KAAK,SAAS,SAC3B,KAAK,SAAS,QAAQ,WAAW,IAAI,KACrC;AACN,QAAI,CAAC,cAAc,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,OAAO;AACnB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK,cAAc,KAAK;AAAA,IACjC;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,YAAN,MAAM,WAAU;AAAA,EACrB,YAAY,MAAM;AAChB,SAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI;AACnC,SAAK,SAAS;AACd,QAAI,KAAK,MAAM,iBAAiB;AAC9B,WAAK,SAAS,CAAC;AACf,eAAS,WAAW,OAAO;AACzB,YAAI,UAAU,MAAM,OAAO;AAC3B,YAAI,gBAAgB,KAAK,MAAM,gBAAgB,OAAO;AACtD,YAAI,WAAW,OAAO,OAAO,CAAC,GAAG,IAAI;AACrC,iBAAS,KAAK,eAAe;AAC3B,mBAAS,CAAC,IAAI,cAAc,CAAC;AAAA,QAC/B;AACA,eAAO,SAAS;AAChB,iBAAS,WAAW,IAAI,SAAS,OAAO,IAAI;AAC5C,aAAK,OAAO,KAAK,IAAI,WAAU,QAAQ,CAAC;AAAA,MAC1C;AAAA,IACF;AACA,SAAK,aAAa,SAAS,KAAK,KAAK;AACrC,aAAS,OAAO,KAAK,YAAY;AAC/B,WAAK,GAAG,IAAI,KAAK,WAAW,GAAG;AAAA,IACjC;AACA,SAAK,cAAc,KAAK,MAAM;AAC9B,SAAK,aAAa,KAAK,MAAM,YAAY,CAAC;AAC1C,WAAO,OAAO,IAAI;AAAA,EACpB;AAAA,EAEA,QAAQ,SAAS;AACf,QAAI,WAAW,WAAW,YAAY,KAAK,QAAQ;AACjD,aAAO,KAAK,OAAO,UAAU,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA,EAEA,cAAc;AACZ,QAAI,kBAAkB,gBAAgB,GACpC,IAAI,EAAG,MAAM,kBAAmB,KAAK,MAAM,SAAS,QAAQ,CAAC,GAC7D,IAAI,EAAG,MAAM,kBAAmB,KAAK,MAAM,SAAS,QAAQ,CAAC;AAC/D,WAAO,GAAG,CAAC,KAAK,CAAC;AAAA,EACnB;AAAA,EAEA,YAAY;AACV,WAAO,CAAC,KAAK,MAAM,EAAE,OAAO,KAAK,WAAW,EAAE,OAAO,OAAO,EAAE,KAAK,IAAI;AAAA,EACzE;AACF;AAEO,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,YAAY,OAAO,MAAMC,MAAK,QAAQ,UAAU,cAAc,WAAW;AACvE,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAOA;AACZ,SAAK,YAAY;AAEjB,SAAK,YAAY,KAAK,WAAW;AACjC,SAAK,WAAW,KAAK,UAAU;AAC/B,SAAK,WAAW,KAAK,UAAU,SAAS;AACxC,SAAK,UAAU,KAAK,SAAS;AAC7B,SAAK,QAAQ,iBAAiB,OAAO,MAAM,aAAa;AACxD,SAAK,YAAY,MAAM,UAAU;AAEjC,WAAO,OAAO,IAAI;AAAA,EACpB;AAAA,EAEA,WAAW;AACT,WAAO,KAAK,OAAO,QAAQ,KAAK,KAAK;AAAA,EACvC;AAAA,EAEA,aAAa;AACX,WACE,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,KAAK,UAAU,KAAK,KAAK;AAAA,EAErE;AAAA,EAEA,YAAY;AACV,WAAO,CAAC,eAAe,KAAK,MAAM,gBAAgB,KAAK,WAAW,CAAC;AAAA,EACrE;AAAA,EAEA,UAAU,WAAW;AACnB,QAAI,WAAW,CAAC;AAChB,QAAI,KAAK,UAAU,GAAG;AACpB,iBAAW;AAAA,QACT,iBAAiB,SAAS,KAAK,SAAS,EAAE,MAAM,WAAW;AAAA,QAC3D,gBAAgB;AAAA,QAChB,OAAO,YAAY;AAAA,QACnB,QAAQ,YAAY;AAAA,MACtB;AAAA,IACF,WAAW,KAAK,UAAU,KAAK,CAAC,KAAK,UAAU,GAAG;AAChD,iBAAW;AAAA,QACT,oBAAoB,KAAK,SAAS,EAAE,YAAY;AAAA,MAClD;AAAA,IACF;AACA,QAAI,WAAW;AACb,UAAI,KAAK,UAAU,GAAG;AAEpB,mBAAW,OAAO,OAAO,UAAU;AAAA;AAAA;AAAA;AAAA,UAIjC,UAAU,KAAK,MAAM,YAAY,OAAO,EAAE,IAAI,KAAK;AAAA,QACrD,CAAC;AAAA,MACH,OAAO;AAEL,mBAAW,OAAO,OAAO,UAAU;AAAA,UACjC,OAAO,YAAY;AAAA,UACnB,QAAQ,YAAY;AAAA,QACtB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EAEA,WAAW;AACT,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO,KAAK,SAAS,EAAE;AAAA,IACzB;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,KAAK,YAAY,KAAK,UAAU,KAAK,SAAS,CAAC,IAAI;AAAA,EAC5D;AAAA,EAEA,YAAY;AACV,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,YAAY;AACV,WAAO,KAAK,SAAS,EAAE;AAAA,EACzB;AAAA,EAEA,YAAY;AACV,QAAI,CAAC,KAAK,SAAS,EAAE,OAAO;AAE1B,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,SAAS,EAAE,MAAM,aAAa,KAAK,IAAI;AAC7D,QAAI,aAAa,QAAW;AAK1B,aAAO;AAAA,IACT;AAIA,WAAO;AAAA,EACT;AAAA,EAEA,aAAa;AACX,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACF;AAEO,SAAS,SAAS,OAAO;AAC9B,MAAI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,KAAK,MAAM,MAAM,YAAY,CAAC,GAC9B,SAAS,IAAI,EAAE;AAEjB,MAAI,QAAQ;AACV,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MAAI,WAAW;AACb,cAAU,cAAc,SAAS;AAAA,EACnC;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,QAAQ,YAAY;AAAA,IAC7B,MAAM,cAAc,kBAAkB,IAAI;AAAA,IAC1C,QAAQ,gBAAgB,OAAO;AAAA,EACjC;AACF;", "names": ["get", "default", "value", "set"]}