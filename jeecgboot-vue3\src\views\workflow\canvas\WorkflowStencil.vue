<template>
  <div class="workflow-stencil" ref="stencilContainerRef">
    <!-- Stencil 组件面板将在这里渲染 -->
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, watch } from 'vue';
  import { Graph } from '@antv/x6';
  import { Stencil } from '@antv/x6-plugin-stencil';
  import { useWorkflowNodes } from './hooks/useWorkflowNodes';

  interface Props {
    graph: Graph | null;
    width?: number;
    height?: number;
  }

  interface Emits {
    (e: 'ready', stencil: Stencil): void;
    (e: 'nodeAdded', nodeData: any): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    width: 200,
    height: 100,
  });

  const emit = defineEmits<Emits>();

  const stencilContainerRef = ref<HTMLElement | null>(null);
  const stencil = ref<Stencil | null>(null);

  const { registerAllNodes, registerAllEdges, getBaseNodes, getSystemNodes, getLaneNodes } = useWorkflowNodes();

  /**
   * 初始化组件面板
   */
  const initStencil = () => {
    if (!stencilContainerRef.value || !props.graph) {
      return;
    }

    // 确保组件面板尺寸正确
    stencilContainerRef.value.style.width = `${props.width}px`;
    stencilContainerRef.value.style.height = '100%';

    try {
      // 创建 Stencil 实例
      stencil.value = new Stencil({
        title: '流程图',
        target: props.graph,
        stencilGraphWidth: props.width - 20,
        stencilGraphHeight: 170,
        collapsable: true,
        groups: [
          { title: '基础流程图', name: 'group1' },
          { title: '系统设计图', name: 'group2', graphHeight: 250 },
          { title: '泳道节点', name: 'group3' },
        ],
        layoutOptions: {
          columns: 2,
          columnWidth: 80,
          rowHeight: 55,
        },
        // 拖拽配置
        getDragNode(node) {
          // 确保拖拽的节点保持原有的配置
          return node.clone().removeZIndex();
        },
        getDropNode(node) {
          // 确保放置的节点具有正确的交互属性
          const cloned = node.clone();
          // 重新设置交互属性
          cloned.prop('movable', true);
          cloned.prop('resizable', false);
          cloned.prop('rotatable', false);
          return cloned;
        },
      });

      // 添加到DOM
      stencilContainerRef.value.appendChild(stencil.value.container);

      // 加载节点到面板
      loadStencilShapes();

      // 通知父组件 Stencil 已准备就绪
      emit('ready', stencil.value as Stencil);
    } catch (error) {
      // 初始化失败，静默处理
    }
  };

  /**
   * 加载组件到面板
   */
  const loadStencilShapes = () => {
    if (!props.graph || !stencil.value) return;

    try {
      // 基础流程图节点
      const baseNodes = getBaseNodes(props.graph);
      stencil.value.load(baseNodes, 'group1');

      // 系统设计图节点
      const systemNodes = getSystemNodes(props.graph);
      stencil.value.load(systemNodes, 'group2');

      // 泳道相关节点
      const laneNodes = getLaneNodes(props.graph);
      stencil.value.load(laneNodes, 'group3');
    } catch (error) {
      // 加载失败，静默处理
    }
  };

  /**
   * 销毁组件面板
   */
  const destroyStencil = () => {
    if (stencil.value) {
      try {
        stencil.value.dispose();
        stencil.value = null;
      } catch (error) {
        // 销毁失败，静默处理
      }
    }
  };

  /**
   * 重新加载组件面板
   */
  const reloadStencil = () => {
    destroyStencil();
    setTimeout(() => {
      initStencil();
    }, 100);
  };

  /**
   * 添加自定义节点到指定分组
   */
  const addCustomNode = (node: any, groupName: string) => {
    if (!stencil.value || !props.graph) return;

    try {
      const customNode = props.graph.createNode(node);
      stencil.value.load([customNode], groupName);
      emit('nodeAdded', node);
    } catch (error) {
      // 添加失败，静默处理
    }
  };

  /**
   * 清空指定分组的节点
   */
  const clearGroup = (groupName: string) => {
    if (!stencil.value) return;

    try {
      stencil.value.load([], groupName);
    } catch (error) {
      // 清空失败，静默处理
    }
  };

  // 监听 graph 变化，重新初始化 Stencil
  watch(
    () => props.graph,
    (newGraph) => {
      if (newGraph) {
        // 注册节点和边
        registerAllNodes();
        registerAllEdges();

        // 初始化 Stencil
        setTimeout(() => {
          initStencil();
        }, 100);
      } else {
        destroyStencil();
      }
    },
    { immediate: true }
  );

  // 监听容器尺寸变化
  watch([() => props.width, () => props.height], () => {
    if (stencilContainerRef.value) {
      stencilContainerRef.value.style.width = `${props.width}px`;
      // 重新初始化以适应新尺寸
      reloadStencil();
    }
  });

  onMounted(() => {
    // 注册所有节点和边
    registerAllNodes();
    registerAllEdges();
  });

  onUnmounted(() => {
    destroyStencil();
  });

  // 暴露方法给父组件
  defineExpose({
    stencil,
    initStencil,
    destroyStencil,
    reloadStencil,
    addCustomNode,
    clearGroup,
    loadStencilShapes,
  });
</script>

<style lang="less" scoped>
  .workflow-stencil {
    width: 100%;
    height: 100%;
    border-right: 1px solid #dfe3e8;
    position: relative;
    background-color: #fff;
    flex-shrink: 0; // 防止在flex容器中被压缩

    :deep(.x6-widget-stencil) {
      background-color: #fff;
      border: none;
    }

    :deep(.x6-widget-stencil-title) {
      background-color: #fff;
      border-bottom: 1px solid #f0f0f0;
      padding: 12px 16px;
      font-weight: 500;
      color: #333;
    }

    :deep(.x6-widget-stencil-group-title) {
      background-color: #fafafa !important;
      border-bottom: 1px solid #f0f0f0;
      padding: 8px 16px;
      font-size: 12px;
      color: #666;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f5f5 !important;
      }
    }

    :deep(.x6-widget-stencil-group-content) {
      padding: 8px;
    }

    :deep(.x6-widget-stencil-node) {
      border: 1px solid transparent;
      border-radius: 4px;
      transition: all 0.3s;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }
    }

    :deep(.x6-widget-stencil-search) {
      padding: 8px 16px;
      border-bottom: 1px solid #f0f0f0;
    }

    :deep(.x6-widget-stencil-search-text) {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 12px;
      z-index: 0;

      &:focus {
        border-color: #1890ff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
</style>
