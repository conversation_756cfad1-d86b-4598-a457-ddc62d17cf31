#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/dist/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/dist/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/dist/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/dist/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules/rollup-plugin-visualizer/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/rollup-plugin-visualizer@5.14.0_rollup@4.45.1/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../rollup-plugin-visualizer/dist/bin/cli.js" "$@"
else
  exec node  "$basedir/../rollup-plugin-visualizer/dist/bin/cli.js" "$@"
fi
