#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules/commitizen/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules/commitizen/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules/commitizen/bin/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules/commitizen/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/commitizen@4.3.1_@types+node@20.19.9_typescript@4.9.5/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../commitizen/bin/git-cz" "$@"
else
  exec node  "$basedir/../commitizen/bin/git-cz" "$@"
fi
