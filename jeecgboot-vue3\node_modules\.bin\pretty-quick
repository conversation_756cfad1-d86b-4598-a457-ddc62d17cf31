#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules/pretty-quick/lib/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules/pretty-quick/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules/pretty-quick/lib/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules/pretty-quick/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/pretty-quick@4.2.2_prettier@3.6.2/node_modules:/mnt/d/JavaProjectSpace/haerbin_bank_newframework_b/jeecgboot-vue3/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../pretty-quick/lib/cli.mjs" "$@"
else
  exec node  "$basedir/../pretty-quick/lib/cli.mjs" "$@"
fi
