import { Graph, Node, Edge, Cell } from '@antv/x6';
import { Ref } from 'vue';

/**
 * 工作流定义数据结构
 */
export interface WorkflowDefineData {
  id: string;
  businessKey: string;
  name: string;
  definitionView: string;
  isEffective: string;
  delFlag: string;
  createBy: string;
  createTime: string;
  updateBy: string;
  updateTime: string;
  definition: string;
  nodes: string;
  startNodeId: string;
  endNodeId: string;
  edges: string;
  version: string;
}

/**
 * 泳道节点配置
 */
export interface LaneNodeConfig {
  id: string;
  position: { x: number; y: number };
  width: number;
  height: number;
  label: string;
  attrs?: Record<string, any>;
}

/**
 * 工作流节点配置
 */
export interface WorkflowNodeConfig {
  id: string;
  position: { x: number; y: number };
  width: number;
  height: number;
  label: string;
  parent?: string;
  shape: string;
  attrs?: Record<string, any>;
}

/**
 * 工作流边配置
 */
export interface WorkflowEdgeConfig {
  id: string;
  source: string;
  target: string;
  labels?: string[];
  attrs?: Record<string, any>;
  shape?: string;
}

/**
 * 泳道数据结构
 */
export interface SwimLaneData {
  lanes: LaneNodeConfig[];
  nodes: WorkflowNodeConfig[];
  edges: WorkflowEdgeConfig[];
}

/**
 * 连接桩配置
 */
export interface PortConfig {
  groups: {
    top: PortGroupConfig;
    right: PortGroupConfig;
    bottom: PortGroupConfig;
    left: PortGroupConfig;
  };
  items: PortItemConfig[];
}

export interface PortGroupConfig {
  position: string;
  attrs: {
    circle: {
      r: number;
      magnet: boolean;
      stroke: string;
      fill: string;
      style: {
        visibility: string;
      };
    };
  };
}

export interface PortItemConfig {
  group: string;
}

/**
 * 节点类型枚举
 */
export enum NodeType {
  CUSTOM_RECT = 'custom-rect',
  LANE = 'lane',
  LANE_RECT = 'lane-rect',
  LANE_POLYGON = 'lane-polygon',
}

/**
 * 边类型枚举
 */
export enum EdgeType {
  LANE_EDGE = 'lane-edge',
}

/**
 * 工具栏操作类型
 */
export enum ToolbarAction {
  UNDO = 'undo',
  REDO = 'redo',
  DELETE = 'delete',
  SAVE = 'save',
  ADD_LANE = 'addLane',
  GENERATE_JSON = 'generateJson',
}

/**
 * 图形实例接口
 * @link useWorkflowGraph
 */
export interface WorkflowGraphInstance {
  graph: Ref<Graph | null>;
  initGraph: (container: HTMLElement) => void;
  destroyGraph: () => void;
  getGraphData: () => any;
  loadGraphData: (data: SwimLaneData) => void;
}

/**
 * 组件面板实例接口
 */
export interface WorkflowStencilInstance {
  stencil: any;
  initStencil: (container: HTMLElement, graph: Graph) => void;
  destroyStencil: () => void;
}

/**
 * 事件处理器接口
 */
export interface WorkflowEventHandlers {
  onCellClick: (args: { e: Event; x: number; y: number; cell: Cell; view: any }) => void;
  onNodeClick: (args: { e: Event; x: number; y: number; node: Node; view: any }) => void;
  onEdgeClick: (args: { e: Event; x: number; y: number; edge: Edge; view: any }) => void;
  onBlankClick: (args: { e: Event; x: number; y: number }) => void;
  onNodeMouseEnter: (args: { e: Event; node: Node; view: any }) => void;
  onNodeMouseLeave: (args: { e: Event; node: Node; view: any }) => void;
}

/**
 * 工作流版本信息
 */
export interface WorkflowVersion {
  id: string;
  timestamp: Date;
  data: any;
  description: string;
}

/**
 * 属性面板数据
 */
export interface NodePropertyData {
  name: string;
  assignee: string;
  assigneeOrgCode: string;
  handler: string;
  type: string;
}
